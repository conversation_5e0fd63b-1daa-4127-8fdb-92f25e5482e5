# Debug Bubble Request Flow

## Test Steps to Trace the Issue

### Step 1: Send Bubble Start Request
1. **Open the app and navigate to a contact's profile**
2. **Click "Start a bubble together"**
3. **Watch the console logs for these messages:**
   ```
   🚀 UnifiedProfileBloc._onSendBubbleRequest: Starting bubble request
   🔄 UnifiedProfileBloc._onSendBubbleRequest: Sending request to backend
   ✅ UnifiedProfileBloc._onSendBubbleRequest: Request sent successfully
   📝 UnifiedProfileBloc._onSendBubbleRequest: Updated local state
   🎯 UnifiedProfileBloc._onSendBubbleRequest: Emitting UnifiedProfileBubbleRequestSent state
   ```
4. **Verify the button shows "Bubble start request sent"**

### Step 2: Navigate Away and Back
1. **Navigate away from the profile (go to home/contacts)**
2. **Navigate back to the same contact's profile**
3. **Watch the console logs for these messages:**
   ```
   🔄 UnifiedProfileBloc._onLoadUnifiedProfile: Loading profile for {userId}
   👤 UnifiedProfileBloc._onLoadUnifiedProfile: Current user: {currentUserId}, Target user: {targetUserId}
   🌐 UserRepository.getEnhancedUser: Fetching enhanced profile for {currentUserId}
   📊 UserRepository.getEnhancedUser: Raw API response for {currentUserId}:
   🔄 UserRepository.getEnhancedUser: Mapped UserModel for {currentUserId}:
   🔍 UnifiedProfileBloc._onLoadUnifiedProfile: Checking for pending requests...
   🔍 UnifiedProfileBloc._checkForPendingRequests: Checking backend for bubble requests to {targetUserId}
   🔍 DEBUG: Enhanced user data for {currentUserId}:
   ```

### Step 3: Analyze the Data
Look for these specific data points in the logs:

#### Expected Backend Response (if working correctly):
```
📊 UserRepository.getEnhancedUser: Raw API response for {currentUserId}:
  - pendingSentBubbleStartRequestUserIds: [{targetUserId}]
```

#### Expected Frontend Processing (if working correctly):
```
🔍 DEBUG: Enhanced user data for {currentUserId}:
  - pendingSentBubbleStartRequestUserIds: [{targetUserId}]
🔍 UnifiedProfileBloc._checkForPendingRequests: ✅ Found pending sent bubble START request from current user to {targetUserId}
📤 UnifiedProfileBloc._onLoadUnifiedProfile: Found pending request state: UnifiedProfileBubbleRequestSent
```

#### If Issue is in Backend:
```
📊 UserRepository.getEnhancedUser: Raw API response for {currentUserId}:
  - pendingSentBubbleStartRequestUserIds: [] // Empty or null
```

#### If Issue is in Frontend Processing:
```
📊 UserRepository.getEnhancedUser: Raw API response for {currentUserId}:
  - pendingSentBubbleStartRequestUserIds: [{targetUserId}] // Correct data from backend
🔍 DEBUG: Enhanced user data for {currentUserId}:
  - pendingSentBubbleStartRequestUserIds: [] // But empty in frontend
```

## Possible Root Causes

### 1. Backend Not Storing Request
- The bubble start request is not being saved to the database
- The `AddUserPendingSentBubbleRequest` is not being called
- The request is being saved but not in the correct format

### 2. Backend Not Returning Request Data
- The enhanced profile endpoint is not including the pending request data
- The field mapping is incorrect in the backend response
- The request data is being filtered out

### 3. Frontend Not Processing Request Data
- The API response is not being parsed correctly
- The field mapping in ApiUserProfile is incorrect
- The UserModel mapping is not working

### 4. Timing Issue
- The backend hasn't processed the request yet when the enhanced profile is fetched
- There's a race condition between request creation and profile loading

## Next Steps Based on Logs

### If Backend Data is Empty:
1. Check if the bubble start request is actually reaching the backend
2. Verify the database contains the request
3. Check if the enhanced profile endpoint is querying the correct data

### If Backend Data is Correct but Frontend is Empty:
1. Check the JSON field mapping in ApiUserProfile
2. Verify the UserModel mapping is working
3. Check for any data transformation issues

### If All Data is Correct but State is Wrong:
1. Check the logic in `_checkForPendingRequests`
2. Verify the condition checking is working correctly
3. Look for any state management issues
