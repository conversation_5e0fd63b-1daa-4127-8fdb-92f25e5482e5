# Bubble Request State Synchronization Test

## Issue Description
When a user sends a bubble start request and then navigates away from the profile and back, the button incorrectly shows "Start a bubble together" instead of "Bubble request sent".

## Root Cause
The local tracking (`_sentBubbleRequests` map) in `UnifiedProfileBloc` was taking precedence over the backend state (`pendingSentBubbleRequestUserIds`) in the `_checkForPendingRequests` method.

## Fix Applied
**Root Cause**: Two issues were identified and fixed:

### Issue 1: Field name mismatch between backend and frontend
- **Backend sends**: `pending_sent_bubble_start_request_user_ids`
- **Frontend was checking**: `pending_sent_bubble_request_user_ids` (legacy field)

### Issue 2: Incorrect button text in UI
- **Documentation requires**: "Bubble start request sent" (line 30)
- **U<PERSON> was showing**: "Bubble request sent" (incorrect)

**Solution**:
1. **Updated UnifiedProfileBloc** to check `pendingSentBubbleStartRequestUserIds` instead of legacy field
2. **Added specific bubble request fields** to ApiUserProfile model
3. **Updated UserModel mapping** to include the specific bubble start request fields
4. **Fixed UI button text** to show correct text based on request type:
   - `startBubbleTogether` → "Bubble start request sent"
   - `inviteToOwnBubble` → "Bubble invite request sent"
   - `requestToJoinBubble` → "Bubble join request sent"
5. **No technical debt** - using the proper specific field names as intended

## Manual Test Steps

### Prerequisites
- Two test accounts (User A and User B)
- Both users should be contacts
- Both users should NOT be in any bubbles

### Test Scenario 1: Backend State Priority
1. **Login as User A**
2. **Navigate to User B's profile**
3. **Verify button shows**: "Start a bubble together"
4. **Click the button** to send bubble start request
5. **Verify button changes to**: "Bubble start request sent"
6. **Navigate away** from the profile (go to home/contacts)
7. **Navigate back** to User B's profile
8. **Expected Result**: Button should still show "Bubble start request sent" ✅
9. **Previous Bug**: Button would incorrectly show "Start a bubble together" ❌

### Test Scenario 2: Request Acceptance
1. **Continue from Scenario 1**
2. **Login as User B** (on different device/browser)
3. **Accept the bubble start request** via notification dialog
4. **Switch back to User A**
5. **Navigate to User B's profile**
6. **Expected Result**: Button should show "Message" or "Bubbler" (since they're now in a bubble together)

### Test Scenario 3: Request Decline
1. **Repeat Scenario 1** steps 1-7
2. **Login as User B** (on different device/browser)
3. **Decline the bubble start request** via notification dialog
4. **Switch back to User A**
5. **Navigate to User B's profile**
6. **Expected Result**: Button should show "Start a bubble together" (allowing to send another request)

### Test Scenario 4: Stale Local State Cleanup
1. **Simulate stale local state** by:
   - Sending a bubble request
   - Having it accepted/declined on backend
   - But local state not updated (e.g., app was backgrounded)
2. **Navigate to the profile**
3. **Expected Result**: Button should reflect the actual backend state, not stale local state

## Technical Verification

### Code Changes Made

#### 1. Updated UnifiedProfileBloc to use correct field name
```dart
// OLD: Checking legacy field (incorrect)
if (currentUser?.pendingSentBubbleRequestUserIds?.contains(targetUserId) == true) {

// NEW: Checking specific bubble start request field (correct)
if (currentUser?.pendingSentBubbleStartRequestUserIds?.contains(targetUserId) == true) {
```

#### 2. Added specific bubble request fields to ApiUserProfile
```dart
// Added to ApiUserProfile model:
@JsonKey(name: 'pending_sent_bubble_start_request_user_ids')
final List<String>? pendingSentBubbleStartRequestUserIds;
@JsonKey(name: 'pending_received_bubble_start_request_user_ids')
final List<String>? pendingReceivedBubbleStartRequestUserIds;
```

#### 3. Updated UserModel mapping
```dart
// Added to _mapApiUserToUserModel:
pendingSentBubbleStartRequestUserIds: apiUser.pendingSentBubbleStartRequestUserIds ?? [],
pendingReceivedBubbleStartRequestUserIds: apiUser.pendingReceivedBubbleStartRequestUserIds ?? [],
```

#### 4. Fixed UI button text based on request type
```dart
// OLD: Fixed text for all bubble requests
actualBottomButtonText = 'Bubble request sent';

// NEW: Dynamic text based on request type
switch (state.requestType) {
  case BubbleRequestType.startBubbleTogether:
    actualBottomButtonText = 'Bubble start request sent';
    break;
  case BubbleRequestType.inviteToOwnBubble:
    actualBottomButtonText = 'Bubble invite request sent';
    break;
  case BubbleRequestType.requestToJoinBubble:
    actualBottomButtonText = 'Bubble join request sent';
    break;
}
```

### Debug Logs to Watch
Look for these log messages in the console:
- `🔍 UnifiedProfileBloc._checkForPendingRequests: Checking backend for bubble requests to {userId}`
- `🔍 UnifiedProfileBloc._checkForPendingRequests: Found pending sent bubble START request from current user to {userId}`
- `🔍 UnifiedProfileBloc._checkForPendingRequests: Clearing stale local bubble request state for {userId}`

## Success Criteria
- ✅ Button state persists correctly across navigation
- ✅ Backend state takes precedence over local state
- ✅ Local state is synchronized with backend state
- ✅ Stale local state is cleaned up automatically
- ✅ No UI inconsistencies when navigating back to profiles

## Related Files Modified
- `hopen/lib/statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc.dart`
  - Modified `_checkForPendingRequests` method to use `pendingSentBubbleStartRequestUserIds`
- `hopen/lib/provider/models/api_models.dart`
  - Added specific bubble request fields to ApiUserProfile model
- `hopen/lib/provider/repositories/user/user_repository_impl.dart`
  - Updated UserModel mapping to include specific bubble request fields
