import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:hopen/statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc.dart';
import 'package:hopen/statefulbusinesslogic/bloc/unified_profile/unified_profile_event.dart';
import 'package:hopen/statefulbusinesslogic/bloc/unified_profile/unified_profile_state.dart';
import 'package:hopen/repositories/user/user_repository.dart';
import 'package:hopen/repositories/bubble/bubble_repository.dart';
import 'package:hopen/repositories/contact/contact_request_repository.dart';
import 'package:hopen/statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import 'package:hopen/statefulbusinesslogic/bloc/auth/auth_state.dart';
import 'package:hopen/statefulbusinesslogic/core/models/user_model.dart';
import 'package:hopen/statefulbusinesslogic/core/models/relationship_type.dart';
import 'package:hopen/provider/services/request_state_manager.dart';

import 'unified_profile_bloc_test.mocks.dart';

@GenerateMocks([
  UserRepository,
  BubbleRepository,
  ContactRequestRepository,
  AuthBloc,
  RequestStateManager,
])
void main() {
  group('UnifiedProfileBloc', () {
    late UnifiedProfileBloc bloc;
    late MockUserRepository mockUserRepository;
    late MockBubbleRepository mockBubbleRepository;
    late MockContactRequestRepository mockContactRequestRepository;
    late MockAuthBloc mockAuthBloc;
    late MockRequestStateManager mockRequestStateManager;

    const String currentUserId = 'current-user-id';
    const String targetUserId = 'target-user-id';

    setUp(() {
      mockUserRepository = MockUserRepository();
      mockBubbleRepository = MockBubbleRepository();
      mockContactRequestRepository = MockContactRequestRepository();
      mockAuthBloc = MockAuthBloc();
      mockRequestStateManager = MockRequestStateManager();

      // Mock auth state
      when(mockAuthBloc.state).thenReturn(
        const AuthState(
          status: AuthStatus.authenticated,
          userId: currentUserId,
        ),
      );

      bloc = UnifiedProfileBloc(
        userRepository: mockUserRepository,
        bubbleRepository: mockBubbleRepository,
        contactRequestRepository: mockContactRequestRepository,
        authBloc: mockAuthBloc,
        requestStateManager: mockRequestStateManager,
      );
    });

    tearDown(() {
      bloc.close();
    });

    group('Bubble Request State Synchronization', () {
      test('should prioritize backend state over local state when loading profile', () async {
        // Arrange
        final targetUser = UserModel(
          id: targetUserId,
          firstName: 'Target',
          lastName: 'User',
          contactIds: [currentUserId], // They are contacts
        );

        final currentUser = UserModel(
          id: currentUserId,
          firstName: 'Current',
          lastName: 'User',
          pendingSentBubbleRequestUserIds: [targetUserId], // Backend shows pending request
        );

        when(mockUserRepository.getEnhancedUser(targetUserId))
            .thenAnswer((_) async => targetUser);
        when(mockUserRepository.getEnhancedUser(currentUserId))
            .thenAnswer((_) async => currentUser);
        when(mockRequestStateManager.initializeForUser(userId: currentUserId))
            .thenAnswer((_) async {});

        // Act
        bloc.add(LoadUnifiedProfile(userId: targetUserId));

        // Wait for the bloc to process the event
        await expectLater(
          bloc.stream,
          emitsInOrder([
            isA<UnifiedProfileLoading>(),
            isA<UnifiedProfileBubbleRequestSent>(),
          ]),
        );

        // Assert
        final state = bloc.state as UnifiedProfileBubbleRequestSent;
        expect(state.user.id, equals(targetUserId));
        expect(state.requestType, equals(BubbleRequestType.startBubbleTogether));
      });

      test('should clear stale local state when backend shows no pending requests', () async {
        // Arrange
        final targetUser = UserModel(
          id: targetUserId,
          firstName: 'Target',
          lastName: 'User',
          contactIds: [currentUserId], // They are contacts
        );

        final currentUser = UserModel(
          id: currentUserId,
          firstName: 'Current',
          lastName: 'User',
          pendingSentBubbleRequestUserIds: [], // Backend shows NO pending requests
        );

        when(mockUserRepository.getEnhancedUser(targetUserId))
            .thenAnswer((_) async => targetUser);
        when(mockUserRepository.getEnhancedUser(currentUserId))
            .thenAnswer((_) async => currentUser);
        when(mockRequestStateManager.initializeForUser(userId: currentUserId))
            .thenAnswer((_) async {});

        // Simulate stale local state by sending a request first
        bloc.add(SendBubbleRequestEvent(
          targetUserId: targetUserId,
          requestType: BubbleRequestType.startBubbleTogether,
        ));

        // Wait for request to be processed
        await Future.delayed(const Duration(milliseconds: 100));

        // Act - Load profile again (simulating navigation back)
        bloc.add(LoadUnifiedProfile(userId: targetUserId));

        // Wait for the bloc to process the event
        await expectLater(
          bloc.stream,
          emitsInOrder([
            isA<UnifiedProfileLoading>(),
            isA<UnifiedProfileLoaded>(), // Should be normal loaded state, not request sent
          ]),
        );

        // Assert
        final state = bloc.state as UnifiedProfileLoaded;
        expect(state.user.id, equals(targetUserId));
        expect(state.relationshipType, equals(RelationshipType.contact));
      });
    });
  });
}
