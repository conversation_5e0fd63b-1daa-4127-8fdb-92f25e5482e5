// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in hopen/test/unit_tests/statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:bloc/bloc.dart' as _i15;
import 'package:hopen/provider/local_storage/request_state_cache.dart' as _i17;
import 'package:hopen/provider/services/request_state_manager.dart' as _i16;
import 'package:hopen/repositories/bubble/bubble_repository.dart' as _i8;
import 'package:hopen/repositories/contact/contact_request_repository.dart'
    as _i12;
import 'package:hopen/repositories/user/user_repository.dart' as _i3;
import 'package:hopen/statefulbusinesslogic/bloc/auth/auth_bloc.dart' as _i13;
import 'package:hopen/statefulbusinesslogic/bloc/auth/auth_event.dart' as _i14;
import 'package:hopen/statefulbusinesslogic/bloc/auth/auth_state.dart' as _i2;
import 'package:hopen/statefulbusinesslogic/core/error/result.dart' as _i6;
import 'package:hopen/statefulbusinesslogic/core/models/bubble_entity.dart'
    as _i9;
import 'package:hopen/statefulbusinesslogic/core/models/bubble_request_model.dart'
    as _i11;
import 'package:hopen/statefulbusinesslogic/core/models/user_model.dart' as _i5;
import 'package:hopen/statefulbusinesslogic/core/models/value_objects.dart'
    as _i10;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i7;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthState_0 extends _i1.SmartFake implements _i2.AuthState {
  _FakeAuthState_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [UserRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserRepository extends _i1.Mock implements _i3.UserRepository {
  MockUserRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i5.UserModel?> getUserById(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #getUserById,
          [userId],
        ),
        returnValue: _i4.Future<_i5.UserModel?>.value(),
      ) as _i4.Future<_i5.UserModel?>);

  @override
  _i4.Future<_i5.UserModel?> getCurrentUser() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentUser,
          [],
        ),
        returnValue: _i4.Future<_i5.UserModel?>.value(),
      ) as _i4.Future<_i5.UserModel?>);

  @override
  _i4.Future<_i6.Result<_i5.UserModel>> getCurrentUserSafe() =>
      (super.noSuchMethod(
        Invocation.method(
          #getCurrentUserSafe,
          [],
        ),
        returnValue: _i4.Future<_i6.Result<_i5.UserModel>>.value(
            _i7.dummyValue<_i6.Result<_i5.UserModel>>(
          this,
          Invocation.method(
            #getCurrentUserSafe,
            [],
          ),
        )),
      ) as _i4.Future<_i6.Result<_i5.UserModel>>);

  @override
  _i4.Future<_i5.UserModel?> getUser(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #getUser,
          [userId],
        ),
        returnValue: _i4.Future<_i5.UserModel?>.value(),
      ) as _i4.Future<_i5.UserModel?>);

  @override
  _i4.Future<_i5.UserModel?> getEnhancedUser(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getEnhancedUser,
          [userId],
        ),
        returnValue: _i4.Future<_i5.UserModel?>.value(),
      ) as _i4.Future<_i5.UserModel?>);

  @override
  _i4.Future<Map<String, dynamic>> getUserInfo(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserInfo,
          [userId],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<List<_i5.UserModel>> getUsers(List<String>? userIds) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUsers,
          [userIds],
        ),
        returnValue: _i4.Future<List<_i5.UserModel>>.value(<_i5.UserModel>[]),
      ) as _i4.Future<List<_i5.UserModel>>);

  @override
  _i4.Future<List<_i5.UserModel>> getAllUsers() => (super.noSuchMethod(
        Invocation.method(
          #getAllUsers,
          [],
        ),
        returnValue: _i4.Future<List<_i5.UserModel>>.value(<_i5.UserModel>[]),
      ) as _i4.Future<List<_i5.UserModel>>);

  @override
  _i4.Future<List<_i5.UserModel>> getFriends(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getFriends,
          [userId],
        ),
        returnValue: _i4.Future<List<_i5.UserModel>>.value(<_i5.UserModel>[]),
      ) as _i4.Future<List<_i5.UserModel>>);

  @override
  _i4.Future<void> createUser(_i5.UserModel? user) => (super.noSuchMethod(
        Invocation.method(
          #createUser,
          [user],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> updateUser(_i5.UserModel? user) => (super.noSuchMethod(
        Invocation.method(
          #updateUser,
          [user],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<List<_i5.UserModel>> findUsers({
    String? firstName,
    String? lastName,
    String? email,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findUsers,
          [],
          {
            #firstName: firstName,
            #lastName: lastName,
            #email: email,
          },
        ),
        returnValue: _i4.Future<List<_i5.UserModel>>.value(<_i5.UserModel>[]),
      ) as _i4.Future<List<_i5.UserModel>>);

  @override
  _i4.Future<List<_i5.UserModel>> getUsersInBubble(String? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUsersInBubble,
          [bubbleId],
        ),
        returnValue: _i4.Future<List<_i5.UserModel>>.value(<_i5.UserModel>[]),
      ) as _i4.Future<List<_i5.UserModel>>);

  @override
  _i4.Future<Map<String, dynamic>> getGroupInfo(String? groupId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getGroupInfo,
          [groupId],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<List<_i5.UserModel>> getMutualFriends(
    String? userId1,
    String? userId2,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getMutualFriends,
          [
            userId1,
            userId2,
          ],
        ),
        returnValue: _i4.Future<List<_i5.UserModel>>.value(<_i5.UserModel>[]),
      ) as _i4.Future<List<_i5.UserModel>>);

  @override
  _i4.Future<List<_i5.UserModel>> getMutualContacts(
    String? userId1,
    String? userId2,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getMutualContacts,
          [
            userId1,
            userId2,
          ],
        ),
        returnValue: _i4.Future<List<_i5.UserModel>>.value(<_i5.UserModel>[]),
      ) as _i4.Future<List<_i5.UserModel>>);

  @override
  _i4.Future<void> addFriend(
    String? userId,
    String? friendId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #addFriend,
          [
            userId,
            friendId,
          ],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> sendContactRequest({
    required String? fromUserId,
    required String? toUserId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendContactRequest,
          [],
          {
            #fromUserId: fromUserId,
            #toUserId: toUserId,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> acceptContactRequest({
    required String? fromUserId,
    required String? toUserId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #acceptContactRequest,
          [],
          {
            #fromUserId: fromUserId,
            #toUserId: toUserId,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> rejectContactRequest({
    required String? fromUserId,
    required String? toUserId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #rejectContactRequest,
          [],
          {
            #fromUserId: fromUserId,
            #toUserId: toUserId,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> removeContact({
    required String? userId,
    required String? contactId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeContact,
          [],
          {
            #userId: userId,
            #contactId: contactId,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> blockUser({
    required String? userId,
    required String? targetUserId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #blockUser,
          [],
          {
            #userId: userId,
            #targetUserId: targetUserId,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> unblockUser({
    required String? userId,
    required String? targetUserId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #unblockUser,
          [],
          {
            #userId: userId,
            #targetUserId: targetUserId,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> reportUser({
    required String? reportedUserId,
    required String? reason,
    required String? category,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #reportUser,
          [],
          {
            #reportedUserId: reportedUserId,
            #reason: reason,
            #category: category,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);
}

/// A class which mocks [BubbleRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockBubbleRepository extends _i1.Mock implements _i8.BubbleRepository {
  MockBubbleRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i6.Result<_i9.BubbleEntity>> createBubble({
    required _i10.BubbleName? name,
    required String? description,
    String? locationName,
    double? locationLat,
    double? locationLng,
    int? locationRadius,
    String? customImageUrl,
    String? colorTheme,
    bool? allowInvites,
    bool? requireApproval,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createBubble,
          [],
          {
            #name: name,
            #description: description,
            #locationName: locationName,
            #locationLat: locationLat,
            #locationLng: locationLng,
            #locationRadius: locationRadius,
            #customImageUrl: customImageUrl,
            #colorTheme: colorTheme,
            #allowInvites: allowInvites,
            #requireApproval: requireApproval,
          },
        ),
        returnValue: _i4.Future<_i6.Result<_i9.BubbleEntity>>.value(
            _i7.dummyValue<_i6.Result<_i9.BubbleEntity>>(
          this,
          Invocation.method(
            #createBubble,
            [],
            {
              #name: name,
              #description: description,
              #locationName: locationName,
              #locationLat: locationLat,
              #locationLng: locationLng,
              #locationRadius: locationRadius,
              #customImageUrl: customImageUrl,
              #colorTheme: colorTheme,
              #allowInvites: allowInvites,
              #requireApproval: requireApproval,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<_i9.BubbleEntity>>);

  @override
  _i4.Future<_i6.Result<_i9.BubbleEntity>> getBubble(_i10.BubbleId? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubble,
          [bubbleId],
        ),
        returnValue: _i4.Future<_i6.Result<_i9.BubbleEntity>>.value(
            _i7.dummyValue<_i6.Result<_i9.BubbleEntity>>(
          this,
          Invocation.method(
            #getBubble,
            [bubbleId],
          ),
        )),
      ) as _i4.Future<_i6.Result<_i9.BubbleEntity>>);

  @override
  _i4.Future<_i6.Result<_i9.BubbleEntity>> getBubbleDetailsById(
          _i10.BubbleId? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubbleDetailsById,
          [bubbleId],
        ),
        returnValue: _i4.Future<_i6.Result<_i9.BubbleEntity>>.value(
            _i7.dummyValue<_i6.Result<_i9.BubbleEntity>>(
          this,
          Invocation.method(
            #getBubbleDetailsById,
            [bubbleId],
          ),
        )),
      ) as _i4.Future<_i6.Result<_i9.BubbleEntity>>);

  @override
  _i4.Future<_i6.Result<void>> updateBubble(
    _i10.BubbleId? bubbleId,
    Map<String, dynamic>? updates,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateBubble,
          [
            bubbleId,
            updates,
          ],
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #updateBubble,
            [
              bubbleId,
              updates,
            ],
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<void>> deleteBubble(_i10.BubbleId? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteBubble,
          [bubbleId],
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #deleteBubble,
            [bubbleId],
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<_i9.BubbleEntity>> joinBubble({
    required _i10.BubbleId? bubbleId,
    _i10.UserId? userId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #joinBubble,
          [],
          {
            #bubbleId: bubbleId,
            #userId: userId,
          },
        ),
        returnValue: _i4.Future<_i6.Result<_i9.BubbleEntity>>.value(
            _i7.dummyValue<_i6.Result<_i9.BubbleEntity>>(
          this,
          Invocation.method(
            #joinBubble,
            [],
            {
              #bubbleId: bubbleId,
              #userId: userId,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<_i9.BubbleEntity>>);

  @override
  _i4.Future<_i6.Result<void>> leaveBubble({
    required _i10.BubbleId? bubbleId,
    _i10.UserId? userId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #leaveBubble,
          [],
          {
            #bubbleId: bubbleId,
            #userId: userId,
          },
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #leaveBubble,
            [],
            {
              #bubbleId: bubbleId,
              #userId: userId,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<List<_i5.UserModel>>> getBubbleMembers(
          _i10.BubbleId? bubbleId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubbleMembers,
          [bubbleId],
        ),
        returnValue: _i4.Future<_i6.Result<List<_i5.UserModel>>>.value(
            _i7.dummyValue<_i6.Result<List<_i5.UserModel>>>(
          this,
          Invocation.method(
            #getBubbleMembers,
            [bubbleId],
          ),
        )),
      ) as _i4.Future<_i6.Result<List<_i5.UserModel>>>);

  @override
  _i4.Future<_i6.Result<List<_i9.BubbleEntity>>> getUserBubbles(
          _i10.UserId? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserBubbles,
          [userId],
        ),
        returnValue: _i4.Future<_i6.Result<List<_i9.BubbleEntity>>>.value(
            _i7.dummyValue<_i6.Result<List<_i9.BubbleEntity>>>(
          this,
          Invocation.method(
            #getUserBubbles,
            [userId],
          ),
        )),
      ) as _i4.Future<_i6.Result<List<_i9.BubbleEntity>>>);

  @override
  _i4.Future<_i6.Result<List<_i9.BubbleEntity>>> getBubbles(
          _i10.UserId? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBubbles,
          [userId],
        ),
        returnValue: _i4.Future<_i6.Result<List<_i9.BubbleEntity>>>.value(
            _i7.dummyValue<_i6.Result<List<_i9.BubbleEntity>>>(
          this,
          Invocation.method(
            #getBubbles,
            [userId],
          ),
        )),
      ) as _i4.Future<_i6.Result<List<_i9.BubbleEntity>>>);

  @override
  _i4.Future<_i6.Result<List<_i9.BubbleEntity>>> getNearbyBubbles({
    required double? latitude,
    required double? longitude,
    required double? radius,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getNearbyBubbles,
          [],
          {
            #latitude: latitude,
            #longitude: longitude,
            #radius: radius,
          },
        ),
        returnValue: _i4.Future<_i6.Result<List<_i9.BubbleEntity>>>.value(
            _i7.dummyValue<_i6.Result<List<_i9.BubbleEntity>>>(
          this,
          Invocation.method(
            #getNearbyBubbles,
            [],
            {
              #latitude: latitude,
              #longitude: longitude,
              #radius: radius,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<List<_i9.BubbleEntity>>>);

  @override
  _i4.Future<_i6.Result<_i9.BubbleEntity>> updateMemberStatus({
    required _i10.BubbleId? bubbleId,
    required _i10.UserId? memberId,
    bool? isOnline,
    int? unreadMessageCount,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateMemberStatus,
          [],
          {
            #bubbleId: bubbleId,
            #memberId: memberId,
            #isOnline: isOnline,
            #unreadMessageCount: unreadMessageCount,
          },
        ),
        returnValue: _i4.Future<_i6.Result<_i9.BubbleEntity>>.value(
            _i7.dummyValue<_i6.Result<_i9.BubbleEntity>>(
          this,
          Invocation.method(
            #updateMemberStatus,
            [],
            {
              #bubbleId: bubbleId,
              #memberId: memberId,
              #isOnline: isOnline,
              #unreadMessageCount: unreadMessageCount,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<_i9.BubbleEntity>>);

  @override
  _i4.Future<_i6.Result<void>> inviteToBubble({
    required _i10.BubbleId? bubbleId,
    required _i10.UserId? inviterId,
    required List<_i10.UserId>? inviteeIds,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #inviteToBubble,
          [],
          {
            #bubbleId: bubbleId,
            #inviterId: inviterId,
            #inviteeIds: inviteeIds,
          },
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #inviteToBubble,
            [],
            {
              #bubbleId: bubbleId,
              #inviterId: inviterId,
              #inviteeIds: inviteeIds,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<_i9.BubbleEntity>> voteToRemoveMember({
    required _i10.BubbleId? bubbleId,
    required _i10.UserId? voterId,
    required _i10.UserId? targetMemberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #voteToRemoveMember,
          [],
          {
            #bubbleId: bubbleId,
            #voterId: voterId,
            #targetMemberId: targetMemberId,
          },
        ),
        returnValue: _i4.Future<_i6.Result<_i9.BubbleEntity>>.value(
            _i7.dummyValue<_i6.Result<_i9.BubbleEntity>>(
          this,
          Invocation.method(
            #voteToRemoveMember,
            [],
            {
              #bubbleId: bubbleId,
              #voterId: voterId,
              #targetMemberId: targetMemberId,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<_i9.BubbleEntity>>);

  @override
  _i4.Future<_i6.Result<void>> removeFromBubble({
    required _i10.BubbleId? bubbleId,
    required _i10.UserId? memberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeFromBubble,
          [],
          {
            #bubbleId: bubbleId,
            #memberId: memberId,
          },
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #removeFromBubble,
            [],
            {
              #bubbleId: bubbleId,
              #memberId: memberId,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<_i9.BubbleEntity>> updateBubbleInfo({
    required _i10.BubbleId? bubbleId,
    _i10.BubbleName? name,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateBubbleInfo,
          [],
          {
            #bubbleId: bubbleId,
            #name: name,
            #endDate: endDate,
          },
        ),
        returnValue: _i4.Future<_i6.Result<_i9.BubbleEntity>>.value(
            _i7.dummyValue<_i6.Result<_i9.BubbleEntity>>(
          this,
          Invocation.method(
            #updateBubbleInfo,
            [],
            {
              #bubbleId: bubbleId,
              #name: name,
              #endDate: endDate,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<_i9.BubbleEntity>>);

  @override
  _i4.Future<_i6.Result<void>> markAllMessagesRead({
    required _i10.BubbleId? bubbleId,
    required _i10.UserId? memberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #markAllMessagesRead,
          [],
          {
            #bubbleId: bubbleId,
            #memberId: memberId,
          },
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #markAllMessagesRead,
            [],
            {
              #bubbleId: bubbleId,
              #memberId: memberId,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<void>> proposeMember({
    required _i10.BubbleId? bubbleId,
    required String? memberName,
    required String? memberEmail,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #proposeMember,
          [],
          {
            #bubbleId: bubbleId,
            #memberName: memberName,
            #memberEmail: memberEmail,
          },
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #proposeMember,
            [],
            {
              #bubbleId: bubbleId,
              #memberName: memberName,
              #memberEmail: memberEmail,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<void>> startCall({
    required _i10.BubbleId? bubbleId,
    required String? callId,
    required List<_i10.UserId>? participants,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #startCall,
          [],
          {
            #bubbleId: bubbleId,
            #callId: callId,
            #participants: participants,
          },
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #startCall,
            [],
            {
              #bubbleId: bubbleId,
              #callId: callId,
              #participants: participants,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<void>> endCall({
    required _i10.BubbleId? bubbleId,
    required String? callId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #endCall,
          [],
          {
            #bubbleId: bubbleId,
            #callId: callId,
          },
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #endCall,
            [],
            {
              #bubbleId: bubbleId,
              #callId: callId,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<_i11.BubbleRequestModel>> createRequest({
    required String? bubbleId,
    required String? targetId,
    required String? type,
    String? message,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createRequest,
          [],
          {
            #bubbleId: bubbleId,
            #targetId: targetId,
            #type: type,
            #message: message,
          },
        ),
        returnValue: _i4.Future<_i6.Result<_i11.BubbleRequestModel>>.value(
            _i7.dummyValue<_i6.Result<_i11.BubbleRequestModel>>(
          this,
          Invocation.method(
            #createRequest,
            [],
            {
              #bubbleId: bubbleId,
              #targetId: targetId,
              #type: type,
              #message: message,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<_i11.BubbleRequestModel>>);

  @override
  _i4.Future<_i6.Result<_i11.BubbleRequestModel>> respondToRequest({
    required String? requestId,
    required String? status,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #respondToRequest,
          [],
          {
            #requestId: requestId,
            #status: status,
          },
        ),
        returnValue: _i4.Future<_i6.Result<_i11.BubbleRequestModel>>.value(
            _i7.dummyValue<_i6.Result<_i11.BubbleRequestModel>>(
          this,
          Invocation.method(
            #respondToRequest,
            [],
            {
              #requestId: requestId,
              #status: status,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<_i11.BubbleRequestModel>>);

  @override
  _i4.Future<_i6.Result<List<_i11.BubbleRequestModel>>> getPendingRequests() =>
      (super.noSuchMethod(
        Invocation.method(
          #getPendingRequests,
          [],
        ),
        returnValue:
            _i4.Future<_i6.Result<List<_i11.BubbleRequestModel>>>.value(
                _i7.dummyValue<_i6.Result<List<_i11.BubbleRequestModel>>>(
          this,
          Invocation.method(
            #getPendingRequests,
            [],
          ),
        )),
      ) as _i4.Future<_i6.Result<List<_i11.BubbleRequestModel>>>);

  @override
  _i4.Future<void> selectFriends({
    required String? bubbleId,
    required List<String>? selectedUserIds,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectFriends,
          [],
          {
            #bubbleId: bubbleId,
            #selectedUserIds: selectedUserIds,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<_i6.Result<void>> acceptBubbleRequest({
    required String? requestId,
    required String? bubbleId,
    required String? userId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #acceptBubbleRequest,
          [],
          {
            #requestId: requestId,
            #bubbleId: bubbleId,
            #userId: userId,
          },
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #acceptBubbleRequest,
            [],
            {
              #requestId: requestId,
              #bubbleId: bubbleId,
              #userId: userId,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<void>> declineBubbleRequest({
    required String? requestId,
    required String? bubbleId,
    required String? userId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #declineBubbleRequest,
          [],
          {
            #requestId: requestId,
            #bubbleId: bubbleId,
            #userId: userId,
          },
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #declineBubbleRequest,
            [],
            {
              #requestId: requestId,
              #bubbleId: bubbleId,
              #userId: userId,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<void>> acceptBubbleKickoutRequest({
    required String? requestId,
    required String? bubbleId,
    required String? targetMemberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #acceptBubbleKickoutRequest,
          [],
          {
            #requestId: requestId,
            #bubbleId: bubbleId,
            #targetMemberId: targetMemberId,
          },
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #acceptBubbleKickoutRequest,
            [],
            {
              #requestId: requestId,
              #bubbleId: bubbleId,
              #targetMemberId: targetMemberId,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<void>> declineBubbleKickoutRequest({
    required String? requestId,
    required String? bubbleId,
    required String? targetMemberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #declineBubbleKickoutRequest,
          [],
          {
            #requestId: requestId,
            #bubbleId: bubbleId,
            #targetMemberId: targetMemberId,
          },
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #declineBubbleKickoutRequest,
            [],
            {
              #requestId: requestId,
              #bubbleId: bubbleId,
              #targetMemberId: targetMemberId,
            },
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);
}

/// A class which mocks [ContactRequestRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockContactRequestRepository extends _i1.Mock
    implements _i12.ContactRequestRepository {
  MockContactRequestRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i6.Result<_i12.ContactRequest>> sendContactRequest(
    String? receiverId,
    String? message,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendContactRequest,
          [
            receiverId,
            message,
          ],
        ),
        returnValue: _i4.Future<_i6.Result<_i12.ContactRequest>>.value(
            _i7.dummyValue<_i6.Result<_i12.ContactRequest>>(
          this,
          Invocation.method(
            #sendContactRequest,
            [
              receiverId,
              message,
            ],
          ),
        )),
      ) as _i4.Future<_i6.Result<_i12.ContactRequest>>);

  @override
  _i4.Future<_i6.Result<List<_i12.ContactRequest>>>
      getPendingReceivedRequests() => (super.noSuchMethod(
            Invocation.method(
              #getPendingReceivedRequests,
              [],
            ),
            returnValue:
                _i4.Future<_i6.Result<List<_i12.ContactRequest>>>.value(
                    _i7.dummyValue<_i6.Result<List<_i12.ContactRequest>>>(
              this,
              Invocation.method(
                #getPendingReceivedRequests,
                [],
              ),
            )),
          ) as _i4.Future<_i6.Result<List<_i12.ContactRequest>>>);

  @override
  _i4.Future<_i6.Result<List<_i12.ContactRequest>>> getPendingSentRequests() =>
      (super.noSuchMethod(
        Invocation.method(
          #getPendingSentRequests,
          [],
        ),
        returnValue: _i4.Future<_i6.Result<List<_i12.ContactRequest>>>.value(
            _i7.dummyValue<_i6.Result<List<_i12.ContactRequest>>>(
          this,
          Invocation.method(
            #getPendingSentRequests,
            [],
          ),
        )),
      ) as _i4.Future<_i6.Result<List<_i12.ContactRequest>>>);

  @override
  _i4.Future<_i6.Result<void>> acceptContactRequest(String? requestId) =>
      (super.noSuchMethod(
        Invocation.method(
          #acceptContactRequest,
          [requestId],
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #acceptContactRequest,
            [requestId],
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<void>> rejectContactRequest(String? requestId) =>
      (super.noSuchMethod(
        Invocation.method(
          #rejectContactRequest,
          [requestId],
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #rejectContactRequest,
            [requestId],
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<void>> cancelContactRequest(String? requestId) =>
      (super.noSuchMethod(
        Invocation.method(
          #cancelContactRequest,
          [requestId],
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #cancelContactRequest,
            [requestId],
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);

  @override
  _i4.Future<_i6.Result<_i12.ContactRequest>> getContactRequest(
          String? requestId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getContactRequest,
          [requestId],
        ),
        returnValue: _i4.Future<_i6.Result<_i12.ContactRequest>>.value(
            _i7.dummyValue<_i6.Result<_i12.ContactRequest>>(
          this,
          Invocation.method(
            #getContactRequest,
            [requestId],
          ),
        )),
      ) as _i4.Future<_i6.Result<_i12.ContactRequest>>);

  @override
  _i4.Future<_i6.Result<bool>> hasPendingRequest(
    String? userId1,
    String? userId2,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #hasPendingRequest,
          [
            userId1,
            userId2,
          ],
        ),
        returnValue:
            _i4.Future<_i6.Result<bool>>.value(_i7.dummyValue<_i6.Result<bool>>(
          this,
          Invocation.method(
            #hasPendingRequest,
            [
              userId1,
              userId2,
            ],
          ),
        )),
      ) as _i4.Future<_i6.Result<bool>>);

  @override
  _i4.Future<_i6.Result<List<_i12.ContactRequest>>>
      getContactRequestHistory() => (super.noSuchMethod(
            Invocation.method(
              #getContactRequestHistory,
              [],
            ),
            returnValue:
                _i4.Future<_i6.Result<List<_i12.ContactRequest>>>.value(
                    _i7.dummyValue<_i6.Result<List<_i12.ContactRequest>>>(
              this,
              Invocation.method(
                #getContactRequestHistory,
                [],
              ),
            )),
          ) as _i4.Future<_i6.Result<List<_i12.ContactRequest>>>);

  @override
  _i4.Future<_i6.Result<List<_i5.UserModel>>> getMutualContacts(
          String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getMutualContacts,
          [userId],
        ),
        returnValue: _i4.Future<_i6.Result<List<_i5.UserModel>>>.value(
            _i7.dummyValue<_i6.Result<List<_i5.UserModel>>>(
          this,
          Invocation.method(
            #getMutualContacts,
            [userId],
          ),
        )),
      ) as _i4.Future<_i6.Result<List<_i5.UserModel>>>);

  @override
  _i4.Future<_i6.Result<List<_i5.UserModel>>> searchUsers(String? query) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchUsers,
          [query],
        ),
        returnValue: _i4.Future<_i6.Result<List<_i5.UserModel>>>.value(
            _i7.dummyValue<_i6.Result<List<_i5.UserModel>>>(
          this,
          Invocation.method(
            #searchUsers,
            [query],
          ),
        )),
      ) as _i4.Future<_i6.Result<List<_i5.UserModel>>>);

  @override
  _i4.Future<_i6.Result<List<_i5.UserModel>>> getSuggestedContacts() =>
      (super.noSuchMethod(
        Invocation.method(
          #getSuggestedContacts,
          [],
        ),
        returnValue: _i4.Future<_i6.Result<List<_i5.UserModel>>>.value(
            _i7.dummyValue<_i6.Result<List<_i5.UserModel>>>(
          this,
          Invocation.method(
            #getSuggestedContacts,
            [],
          ),
        )),
      ) as _i4.Future<_i6.Result<List<_i5.UserModel>>>);

  @override
  _i4.Future<_i6.Result<void>> expireOldRequests() => (super.noSuchMethod(
        Invocation.method(
          #expireOldRequests,
          [],
        ),
        returnValue:
            _i4.Future<_i6.Result<void>>.value(_i7.dummyValue<_i6.Result<void>>(
          this,
          Invocation.method(
            #expireOldRequests,
            [],
          ),
        )),
      ) as _i4.Future<_i6.Result<void>>);
}

/// A class which mocks [AuthBloc].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthBloc extends _i1.Mock implements _i13.AuthBloc {
  MockAuthBloc() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthState get state => (super.noSuchMethod(
        Invocation.getter(#state),
        returnValue: _FakeAuthState_0(
          this,
          Invocation.getter(#state),
        ),
      ) as _i2.AuthState);

  @override
  _i4.Stream<_i2.AuthState> get stream => (super.noSuchMethod(
        Invocation.getter(#stream),
        returnValue: _i4.Stream<_i2.AuthState>.empty(),
      ) as _i4.Stream<_i2.AuthState>);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
      ) as bool);

  @override
  _i4.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  void add(_i14.AuthEvent? event) => super.noSuchMethod(
        Invocation.method(
          #add,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onEvent(_i14.AuthEvent? event) => super.noSuchMethod(
        Invocation.method(
          #onEvent,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void emit(_i2.AuthState? state) => super.noSuchMethod(
        Invocation.method(
          #emit,
          [state],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void on<E extends _i14.AuthEvent>(
    _i15.EventHandler<E, _i2.AuthState>? handler, {
    _i15.EventTransformer<E>? transformer,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #on,
          [handler],
          {#transformer: transformer},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onTransition(
          _i15.Transition<_i14.AuthEvent, _i2.AuthState>? transition) =>
      super.noSuchMethod(
        Invocation.method(
          #onTransition,
          [transition],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onChange(_i15.Change<_i2.AuthState>? change) => super.noSuchMethod(
        Invocation.method(
          #onChange,
          [change],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addError(
    Object? error, [
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #addError,
          [
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onError(
    Object? error,
    StackTrace? stackTrace,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #onError,
          [
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [RequestStateManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockRequestStateManager extends _i1.Mock
    implements _i16.RequestStateManager {
  MockRequestStateManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i16.RequestStateUpdate> get requestStateUpdates =>
      (super.noSuchMethod(
        Invocation.getter(#requestStateUpdates),
        returnValue: _i4.Stream<_i16.RequestStateUpdate>.empty(),
      ) as _i4.Stream<_i16.RequestStateUpdate>);

  @override
  _i4.Future<_i5.UserModel?> initializeForUser({
    required String? userId,
    bool? forceRefresh = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #initializeForUser,
          [],
          {
            #userId: userId,
            #forceRefresh: forceRefresh,
          },
        ),
        returnValue: _i4.Future<_i5.UserModel?>.value(),
      ) as _i4.Future<_i5.UserModel?>);

  @override
  _i4.Future<_i16.RequestStatus> checkRequestStatus({
    required String? currentUserId,
    required String? targetUserId,
    _i17.RequestType? requestType,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #checkRequestStatus,
          [],
          {
            #currentUserId: currentUserId,
            #targetUserId: targetUserId,
            #requestType: requestType,
          },
        ),
        returnValue:
            _i4.Future<_i16.RequestStatus>.value(_i16.RequestStatus.none),
      ) as _i4.Future<_i16.RequestStatus>);

  @override
  _i4.Future<void> onRequestSent({
    required String? currentUserId,
    required String? targetUserId,
    required _i17.RequestType? requestType,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #onRequestSent,
          [],
          {
            #currentUserId: currentUserId,
            #targetUserId: targetUserId,
            #requestType: requestType,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> onRequestReceived({
    required String? currentUserId,
    required String? senderUserId,
    required _i17.RequestType? requestType,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #onRequestReceived,
          [],
          {
            #currentUserId: currentUserId,
            #senderUserId: senderUserId,
            #requestType: requestType,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> onRequestAccepted({
    required String? currentUserId,
    required String? otherUserId,
    required _i17.RequestType? requestType,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #onRequestAccepted,
          [],
          {
            #currentUserId: currentUserId,
            #otherUserId: otherUserId,
            #requestType: requestType,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> onRequestDeclined({
    required String? currentUserId,
    required String? otherUserId,
    required _i17.RequestType? requestType,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #onRequestDeclined,
          [],
          {
            #currentUserId: currentUserId,
            #otherUserId: otherUserId,
            #requestType: requestType,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<_i5.UserModel?> refreshFromServer(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #refreshFromServer,
          [userId],
        ),
        returnValue: _i4.Future<_i5.UserModel?>.value(),
      ) as _i4.Future<_i5.UserModel?>);

  @override
  _i4.Future<void> clearCache() => (super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
