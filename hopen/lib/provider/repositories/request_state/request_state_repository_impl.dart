import '../../../repositories/request_state/request_state_repository.dart';
import '../../../statefulbusinesslogic/core/error/result.dart';
import '../../../statefulbusinesslogic/core/models/user_model.dart';
import '../../services/request_state_manager.dart';

/// Implementation of RequestStateRepository that delegates to RequestStateManager.
/// 
/// This implementation follows the clean architecture pattern by providing
/// a repository interface that the business logic layer can depend on,
/// while delegating the actual implementation to the RequestStateManager
/// in the provider layer.
class RequestStateRepositoryImpl implements RequestStateRepository {
  final RequestStateManager _requestStateManager;

  RequestStateRepositoryImpl({
    required RequestStateManager requestStateManager,
  }) : _requestStateManager = requestStateManager;

  @override
  Future<Result<RequestStatus>> checkRequestStatus({
    required String currentUserId,
    required String targetUserId,
    required RequestType requestType,
  }) async {
    try {
      final status = await _requestStateManager.checkRequestStatus(
        currentUserId: currentUserId,
        targetUserId: targetUserId,
        requestType: _mapToManagerRequestType(requestType),
      );
      
      return Result.success(_mapFromManagerRequestStatus(status));
    } catch (e) {
      return Result.failure(UnexpectedError(message: 'Failed to check request status: $e'));
    }
  }

  @override
  Future<Result<void>> markRequestSent({
    required String currentUserId,
    required String targetUserId,
    required RequestType requestType,
    String? requestId,
  }) async {
    try {
      await _requestStateManager.markRequestSent(
        currentUserId: currentUserId,
        otherUserId: targetUserId,
        requestType: _mapToManagerRequestType(requestType),
        requestId: requestId,
      );
      
      return const Result.success(null);
    } catch (e) {
      return Result.failure(UnexpectedError(message: 'Failed to mark request as sent: $e'));
    }
  }

  @override
  Future<Result<void>> markRequestCompleted({
    required String currentUserId,
    required String targetUserId,
    required RequestType requestType,
  }) async {
    try {
      await _requestStateManager.markRequestCompleted(
        currentUserId: currentUserId,
        otherUserId: targetUserId,
        requestType: _mapToManagerRequestType(requestType),
      );
      
      return const Result.success(null);
    } catch (e) {
      return Result.failure(UnexpectedError(message: 'Failed to mark request as completed: $e'));
    }
  }

  @override
  Future<Result<void>> markRequestDeclined({
    required String currentUserId,
    required String targetUserId,
    required RequestType requestType,
  }) async {
    try {
      await _requestStateManager.markRequestDeclined(
        currentUserId: currentUserId,
        otherUserId: targetUserId,
        requestType: _mapToManagerRequestType(requestType),
      );
      
      return const Result.success(null);
    } catch (e) {
      return Result.failure(UnexpectedError(message: 'Failed to mark request as declined: $e'));
    }
  }

  @override
  Future<Result<UserModel?>> initializeForUser({
    required String userId,
    bool forceRefresh = false,
  }) async {
    try {
      final userModel = await _requestStateManager.initializeForUser(
        userId: userId,
        forceRefresh: forceRefresh,
      );
      
      return Result.success(userModel);
    } catch (e) {
      return Result.failure(UnexpectedError(message: 'Failed to initialize request state for user: $e'));
    }
  }

  @override
  Future<Result<UserModel?>> refreshFromServer(String userId) async {
    try {
      final userModel = await _requestStateManager.refreshFromServer(userId);
      return Result.success(userModel);
    } catch (e) {
      return Result.failure(UnexpectedError(message: 'Failed to refresh from server: $e'));
    }
  }

  @override
  Future<Result<void>> clearCache() async {
    try {
      await _requestStateManager.clearCache();
      return const Result.success(null);
    } catch (e) {
      return Result.failure(UnexpectedError(message: 'Failed to clear cache: $e'));
    }
  }

  /// Maps repository RequestType to RequestStateManager RequestType.
  RequestType _mapToManagerRequestType(RequestType requestType) {
    // Since both enums have the same values, we can map by name
    switch (requestType) {
      case RequestType.contact:
        return RequestType.contact;
      case RequestType.bubbleStart:
        return RequestType.bubbleStart;
      case RequestType.bubbleInvite:
        return RequestType.bubbleInvite;
      case RequestType.bubbleJoin:
        return RequestType.bubbleJoin;
      case RequestType.bubblePropose:
        return RequestType.bubblePropose;
      case RequestType.bubbleKickout:
        return RequestType.bubbleKickout;
    }
  }

  /// Maps RequestStateManager RequestStatus to repository RequestStatus.
  RequestStatus _mapFromManagerRequestStatus(RequestStatus status) {
    // Since both enums have the same values, we can map by name
    switch (status) {
      case RequestStatus.none:
        return RequestStatus.none;
      case RequestStatus.sentPending:
        return RequestStatus.sentPending;
      case RequestStatus.receivedPending:
        return RequestStatus.receivedPending;
      case RequestStatus.accepted:
        return RequestStatus.accepted;
      case RequestStatus.declined:
        return RequestStatus.declined;
      case RequestStatus.expired:
        return RequestStatus.expired;
    }
  }
}
