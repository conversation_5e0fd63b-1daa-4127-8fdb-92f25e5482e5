import '../../../repositories/bubble/bubble_start_request_repository.dart';
import '../../datasources/http_remote_datasource.dart';
import '../../exceptions/network_exception.dart';


class BubbleStartRequestRepositoryImpl implements BubbleStartRequestRepository {

  BubbleStartRequestRepositoryImpl({
    required HttpRemoteDataSource dataSource,
  }) : _dataSource = dataSource;
  final HttpRemoteDataSource _dataSource;

  @override
  Future<bool> acceptStartRequest({
    required String requestId,
    required String requesterId,
  }) async {
    try {
      // Accept the start request using the backend endpoint
      await _dataSource.post('/bubble/requests/$requestId/accept', {});
      return true;
    } catch (e) {
      throw NetworkException(
        message: 'Failed to accept start request: $e',
        originalError: e,
      );
    }
  }

  @override
  Future<void> declineStartRequest({
    required String requestId,
    required String requesterId,
  }) async {
    try {
      // Decline the start request using the backend endpoint
      await _dataSource.post('/bubble/requests/$requestId/decline', {});
    } catch (e) {
      throw NetworkException(
        message: 'Failed to decline start request: $e',
        originalError: e,
      );
    }
  }

  @override
  Future<void> createStartRequest({
    required String targetUserId,
    String? message,
  }) async {
    try {
      // Create start request using the backend endpoint
      await _dataSource.post('/api/v1/bubbles/start-request', {
        'target_user_id': targetUserId,
        if (message != null) 'message': message,
      });
    } catch (e) {
      throw NetworkException(
        message: 'Failed to create start request: $e',
        originalError: e,
      );
    }
  }
} 