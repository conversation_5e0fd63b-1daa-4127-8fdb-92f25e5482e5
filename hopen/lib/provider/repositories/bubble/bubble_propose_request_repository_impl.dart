import '../../../repositories/bubble/bubble_propose_request_repository.dart';
import '../../../statefulbusinesslogic/core/error/result.dart';
import '../../../statefulbusinesslogic/core/error/app_error.dart';
import '../../datasources/http_remote_datasource.dart';
import '../../exceptions/network_exception.dart';


class BubbleProposeRequestRepositoryImpl implements BubbleProposeRequestRepository {

  BubbleProposeRequestRepositoryImpl({
    required HttpRemoteDataSource dataSource,
  }) : _dataSource = dataSource;
  final HttpRemoteDataSource _dataSource;

  @override
  Future<Result<bool>> acceptProposeRequest({
    required String requestId,
    required String proposedMemberId,
  }) async {
    try {
      // Accept the propose request using the backend endpoint
      await _dataSource.post('/bubble/requests/$requestId/accept', {});
      return Result.success(true);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to accept propose request: $e'));
    }
  }

  @override
  Future<void> declineProposeRequest({
    required String requestId,
    required String proposedMemberId,
  }) async {
    try {
      // Decline the propose request using the backend endpoint
      await _dataSource.post('/bubble/requests/$requestId/decline', {});
    } catch (e) {
      throw NetworkException(
        message: 'Failed to decline propose request: $e',
        originalError: e,
      );
    }
  }
} 