import '../../../repositories/bubble/bubble_join_request_repository.dart';
import '../../../statefulbusinesslogic/core/error/result.dart';
import '../../datasources/http_remote_datasource.dart';


/// Implementation of BubbleJoinRequestRepository using HTTP backend
class BubbleJoinRequestRepositoryImpl implements BubbleJoinRequestRepository {

  BubbleJoinRequestRepositoryImpl({
    required HttpRemoteDataSource remoteDataSource,
  }) : _remoteDataSource = remoteDataSource;
  final HttpRemoteDataSource _remoteDataSource;

  @override
  Future<Result<BubbleJoinRequest>> sendJoinRequest(String bubbleId, String message) async {
    try {
      final response = await _remoteDataSource.post('/api/v1/bubbles/$bubbleId/join', {
        'message': message,
      });
      // Backend returns: {"message": "Join request created successfully", "request_id": "uuid"}
      // Create a minimal request object since backend doesn't return full details
      final request = BubbleJoinRequest(
        id: response['request_id'] as String,
        bubbleId: bubbleId,
        userId: '', // Will be filled by backend when fetching requests
        userName: '', // Will be filled by backend when fetching requests
        userAvatarUrl: null,
        requestedAt: DateTime.now(),
        status: JoinRequestStatus.pending,
        message: message,
        respondedAt: null,
        respondedBy: null,
      );
      return Result.success(request);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to send join request: $e'));
    }
  }

  @override
  Future<Result<List<BubbleJoinRequest>>> getPendingJoinRequests(String bubbleId) async {
    try {
      final response = await _remoteDataSource.get('/bubbles/$bubbleId/join-requests/pending');
      final requests = (response['joinRequests'] as List<dynamic>? ?? [])
          .map((json) => BubbleJoinRequest(
                id: json['id'] as String,
                bubbleId: json['bubbleId'] as String,
                userId: json['userId'],
                userName: json['userName'],
                userAvatarUrl: json['userAvatarUrl'],
                requestedAt: DateTime.parse(json['requestedAt'] as String),
                status: JoinRequestStatus.values.firstWhere(
                  (e) => e.name == json['status'],
                  orElse: () => JoinRequestStatus.pending,
                ),
                message: json['message'] as String?,
                respondedAt: json['respondedAt'] != null ? DateTime.parse(json['respondedAt'] as String) : null,
                respondedBy: json['respondedBy'],
              ),)
          .toList();
      return Result.success(requests);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get pending join requests: $e'));
    }
  }

  @override
  Future<Result<List<BubbleJoinRequest>>> getUserJoinRequests(String userId) async {
    try {
      final response = await _remoteDataSource.get('/users/$userId/join-requests');
      final requests = (response['joinRequests'] as List<dynamic>? ?? [])
          .map((json) => BubbleJoinRequest(
                id: json['id'] as String,
                bubbleId: json['bubbleId'] as String,
                userId: json['userId'],
                userName: json['userName'],
                userAvatarUrl: json['userAvatarUrl'],
                requestedAt: DateTime.parse(json['requestedAt'] as String),
                status: JoinRequestStatus.values.firstWhere(
                  (e) => e.name == json['status'],
                  orElse: () => JoinRequestStatus.pending,
                ),
                message: json['message'] as String?,
                respondedAt: json['respondedAt'] != null ? DateTime.parse(json['respondedAt'] as String) : null,
                respondedBy: json['respondedBy'],
              ),)
          .toList();
      return Result.success(requests);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get user join requests: $e'));
    }
  }

  @override
  Future<Result<void>> respondToJoinRequest(String requestId, bool accept, String? responseMessage) async {
    try {
      await _remoteDataSource.put('/join-requests/$requestId/respond', {
        'accept': accept,
        'responseMessage': responseMessage,
      });
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to respond to join request: $e'));
    }
  }

  @override
  Future<Result<void>> cancelJoinRequest(String requestId) async {
    try {
      await _remoteDataSource.delete('/join-requests/$requestId');
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to cancel join request: $e'));
    }
  }

  @override
  Future<Result<void>> expireOldJoinRequests() async {
    try {
      await _remoteDataSource.post('/join-requests/expire-old', {});
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to expire old join requests: $e'));
    }
  }

  @override
  Future<Result<BubbleJoinRequest>> getBubbleDetails(String bubbleId) async {
    try {
      final response = await _remoteDataSource.get('/bubbles/$bubbleId/details');
      final request = BubbleJoinRequest(
        id: response['id'] as String,
        bubbleId: response['bubbleId'] as String,
        userId: response['userId'],
        userName: response['userName'],
        userAvatarUrl: response['userAvatarUrl'],
        requestedAt: DateTime.parse(response['requestedAt'] as String),
        status: JoinRequestStatus.values.firstWhere(
          (e) => e.name == response['status'],
          orElse: () => JoinRequestStatus.pending,
        ),
        message: response['message'] as String?,
        respondedAt: response['respondedAt'] != null ? DateTime.parse(response['respondedAt'] as String) : null,
        respondedBy: response['respondedBy'],
      );
      return Result.success(request);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get bubble details: $e'));
    }
  }

  @override
  Future<Result<void>> acceptJoinRequest(String bubbleId, String requestId) async {
    try {
      await _remoteDataSource.post('/api/v1/bubbles/$bubbleId/requests/$requestId/accept', {});
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to accept join request: $e'));
    }
  }

  @override
  Future<Result<void>> rejectJoinRequest(String requestId, String reason) async {
    try {
      await _remoteDataSource.put('/join-requests/$requestId/reject', {});
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to reject join request: $e'));
    }
  }

  @override
  Future<Result<BubbleJoinRequest>> getJoinRequest(String requestId) async {
    try {
      final response = await _remoteDataSource.get('/join-requests/$requestId');
      final request = BubbleJoinRequest(
        id: response['id'] as String,
        bubbleId: response['bubbleId'] as String,
        userId: response['userId'],
        userName: response['userName'],
        userAvatarUrl: response['userAvatarUrl'],
        requestedAt: DateTime.parse(response['requestedAt'] as String),
        status: JoinRequestStatus.values.firstWhere(
          (e) => e.name == response['status'],
          orElse: () => JoinRequestStatus.pending,
        ),
        message: response['message'] as String?,
        respondedAt: response['respondedAt'] != null ? DateTime.parse(response['respondedAt'] as String) : null,
        respondedBy: response['respondedBy'],
      );
      return Result.success(request);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get join request: $e'));
    }
  }

  @override
  Future<Result<List<BubbleJoinRequest>>> getJoinRequestHistory(String bubbleId) async {
    try {
      final response = await _remoteDataSource.get('/bubbles/$bubbleId/join-requests/history');
      final List<dynamic> data = response['requests'] ?? [];
      final requests = data.map((json) => BubbleJoinRequest(
        id: json['id'] as String,
        bubbleId: json['bubbleId'] as String,
        userId: json['userId'],
        userName: json['userName'],
        userAvatarUrl: json['userAvatarUrl'],
        requestedAt: DateTime.parse(json['requestedAt'] as String),
        status: JoinRequestStatus.values.firstWhere(
          (e) => e.name == json['status'],
          orElse: () => JoinRequestStatus.pending,
        ),
        message: json['message'] as String?,
        respondedAt: json['respondedAt'] != null ? DateTime.parse(json['respondedAt'] as String) : null,
        respondedBy: json['respondedBy'],
      ),).toList();
      return Result.success(requests);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get join request history: $e'));
    }
  }

  @override
  Future<Result<void>> approveJoinRequest(String requestId) async {
    try {
      await _remoteDataSource.post('/bubble/requests/$requestId/accept', {});
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to approve join request: $e'));
    }
  }

  @override
  Future<Result<void>> declineJoinRequest(String bubbleId, String requestId) async {
    try {
      await _remoteDataSource.post('/bubble/requests/$requestId/decline', {});
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to decline join request: $e'));
    }
  }

  @override
  Future<Result<bool>> hasPendingJoinRequest(String bubbleId, String userId) async {
    try {
      final response = await _remoteDataSource.get('/bubbles/$bubbleId/join-requests/pending/$userId');
      return Result.success(response['hasPendingRequest'] ?? false);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to check pending join request: $e'));
    }
  }
} 