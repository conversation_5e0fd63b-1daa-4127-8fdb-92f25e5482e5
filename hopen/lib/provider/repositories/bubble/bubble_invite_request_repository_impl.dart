import '../../../repositories/bubble/bubble_invite_request_repository.dart';
import '../../../statefulbusinesslogic/core/error/result.dart';
import '../../datasources/http_remote_datasource.dart';


/// Implementation of BubbleInviteRequestRepository using HTTP backend
class BubbleInviteRequestRepositoryImpl implements BubbleInviteRequestRepository {

  BubbleInviteRequestRepositoryImpl({
    required HttpRemoteDataSource remoteDataSource,
  }) : _remoteDataSource = remoteDataSource;
  final HttpRemoteDataSource _remoteDataSource;

  @override
  Future<Result<void>> sendInviteRequest(String bubbleId, String inviteeId, String? message) async {
    try {
      await _remoteDataSource.post('/api/v1/bubbles/$bubbleId/invite', {
        'recipient_id': inviteeId,
        'message': message ?? '',
      });
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to send invite request: $e'));
    }
  }

  @override
  Future<Result<List<BubbleInviteRequest>>> getPendingInviteRequests(String bubbleId) async {
    try {
      final response = await _remoteDataSource.get('/api/bubbles/$bubbleId/invite-requests/pending');
      final data = response['requests'] as List<dynamic>? ?? [];
      final requests = data.map((json) => BubbleInviteRequest(
        id: json['id'] as String,
        bubbleId: json['bubbleId'] as String,
        bubbleName: json['bubbleName'] as String,
        inviterId: json['inviterId'] as String,
        inviterName: json['inviterName'] as String,
        inviteeId: json['inviteeId'] as String,
        inviteeName: json['inviteeName'] as String,
        invitedAt: DateTime.parse(json['invitedAt'] as String),
        status: InviteRequestStatus.values.firstWhere(
          (e) => e.name == json['status'],
          orElse: () => InviteRequestStatus.pending,
        ),
        message: json['message'] as String?,
        respondedAt: json['respondedAt'] != null ? DateTime.parse(json['respondedAt'] as String) : null,
        expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt'] as String) : null,
      ),).toList();
      return Result.success(requests);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get pending invite requests: $e'));
    }
  }

  @override
  Future<Result<List<BubbleInviteRequest>>> getUserInviteRequests(String userId) async {
    try {
      final response = await _remoteDataSource.get('/api/users/$userId/invite-requests');
      final data = response['requests'] as List<dynamic>? ?? [];
      final requests = data.map((json) => BubbleInviteRequest(
        id: json['id'] as String,
        bubbleId: json['bubbleId'] as String,
        bubbleName: json['bubbleName'] as String,
        inviterId: json['inviterId'] as String,
        inviterName: json['inviterName'] as String,
        inviteeId: json['inviteeId'] as String,
        inviteeName: json['inviteeName'] as String,
        invitedAt: DateTime.parse(json['invitedAt'] as String),
        status: InviteRequestStatus.values.firstWhere(
          (e) => e.name == json['status'],
          orElse: () => InviteRequestStatus.pending,
        ),
        message: json['message'] as String?,
        respondedAt: json['respondedAt'] != null ? DateTime.parse(json['respondedAt'] as String) : null,
        expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt'] as String) : null,
      ),).toList();
      return Result.success(requests);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get user invite requests: $e'));
    }
  }

  @override
  Future<Result<void>> respondToInviteRequest(String requestId, InviteRequestStatus response) async {
    try {
      await _remoteDataSource.put('/api/invite-requests/$requestId/respond', {
        'response': response.name,
      });
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to respond to invite request: $e'));
    }
  }

  @override
  Future<Result<void>> cancelInviteRequest(String requestId) async {
    try {
      await _remoteDataSource.delete('/api/invite-requests/$requestId');
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to cancel invite request: $e'));
    }
  }

  @override
  Future<Result<void>> expireOldInviteRequests() async {
    try {
      await _remoteDataSource.post('/api/invite-requests/expire-old', {});
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to expire old invite requests: $e'));
    }
  }

  @override
  Future<Result<Map<String, int>>> getInviteStatistics(String bubbleId) async {
    try {
      final response = await _remoteDataSource.get('/api/bubbles/$bubbleId/invite-statistics');
      return Result.success(Map<String, int>.from(response['statistics'] as Map<dynamic, dynamic>? ?? {}));
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get invite statistics: $e'));
    }
  }

  @override
  Future<Result<void>> acceptInviteRequest(String requestId) async {
    try {
      await _remoteDataSource.post('/bubble/requests/$requestId/accept', {});
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to accept invite request: $e'));
    }
  }

  @override
  Future<Result<void>> declineInviteRequest(String requestId) async {
    try {
      await _remoteDataSource.post('/bubble/requests/$requestId/decline', {});
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to decline invite request: $e'));
    }
  }

  @override
  Future<Result<BubbleInviteRequest>> getBubbleDetails(String bubbleId) async {
    try {
      final response = await _remoteDataSource.get('/api/bubbles/$bubbleId/details');
      final bubbleDetails = BubbleInviteRequest(
        id: response['id'] as String,
        bubbleId: response['bubbleId'] as String,
        bubbleName: response['bubbleName'] as String,
        inviterId: response['inviterId'] as String,
        inviterName: response['inviterName'] as String,
        inviteeId: response['inviteeId'],
        inviteeName: response['inviteeName'],
        invitedAt: DateTime.parse(response['invitedAt'] as String),
        status: InviteRequestStatus.values.firstWhere(
          (e) => e.name == response['status'],
          orElse: () => InviteRequestStatus.pending,
        ),
        message: response['message'] as String?,
        respondedAt: response['respondedAt'] != null ? DateTime.parse(response['respondedAt'] as String) : null,
        expiresAt: response['expiresAt'] != null ? DateTime.parse(response['expiresAt'] as String) : null,
      );
      return Result.success(bubbleDetails);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get bubble details: $e'));
    }
  }

  @override
  Future<Result<BubbleInviteRequest>> sendInvite(String bubbleId, String userId, String message) async {
    try {
      final response = await _remoteDataSource.post('/api/bubbles/$bubbleId/invite', {
        'userId': userId,
        'message': message,
      });
      
      final invite = BubbleInviteRequest(
        id: response['id'] as String,
        bubbleId: response['bubbleId'] as String,
        bubbleName: response['bubbleName'] as String,
        inviterId: response['inviterId'] as String,
        inviterName: response['inviterName'] as String,
        inviteeId: response['inviteeId'],
        inviteeName: response['inviteeName'],
        invitedAt: DateTime.parse(response['invitedAt'] as String),
        status: InviteRequestStatus.values.firstWhere(
          (e) => e.name == response['status'],
          orElse: () => InviteRequestStatus.pending,
        ),
        message: response['message'] as String?,
        respondedAt: response['respondedAt'] != null ? DateTime.parse(response['respondedAt'] as String) : null,
        expiresAt: response['expiresAt'] != null ? DateTime.parse(response['expiresAt'] as String) : null,
      );
      
      return Result.success(invite);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to send invite: $e'));
    }
  }

  @override
  Future<Result<List<BubbleInviteRequest>>> getPendingInvites(String userId) async {
    try {
      final response = await _remoteDataSource.get('/api/users/$userId/pending-invites');
      final List<dynamic> data = response['invites'] ?? [];
      final invites = data.map((json) => BubbleInviteRequest(
        id: json['id'] as String,
        bubbleId: json['bubbleId'] as String,
        bubbleName: json['bubbleName'] as String,
        inviterId: json['inviterId'] as String,
        inviterName: json['inviterName'] as String,
        inviteeId: json['inviteeId'] as String,
        inviteeName: json['inviteeName'] as String,
        invitedAt: DateTime.parse(json['invitedAt'] as String),
        status: InviteRequestStatus.values.firstWhere(
          (e) => e.name == json['status'],
          orElse: () => InviteRequestStatus.pending,
        ),
        message: json['message'] as String?,
        respondedAt: json['respondedAt'] != null ? DateTime.parse(json['respondedAt'] as String) : null,
        expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt'] as String) : null,
      ),).toList();
      return Result.success(invites);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get pending invites: $e'));
    }
  }

  @override
  Future<Result<List<BubbleInviteRequest>>> getSentInvites(String userId) async {
    try {
      final response = await _remoteDataSource.get('/api/users/$userId/sent-invites');
      final List<dynamic> data = response['invites'] ?? [];
      final invites = data.map((json) => BubbleInviteRequest(
        id: json['id'] as String,
        bubbleId: json['bubbleId'] as String,
        bubbleName: json['bubbleName'] as String,
        inviterId: json['inviterId'] as String,
        inviterName: json['inviterName'] as String,
        inviteeId: json['inviteeId'] as String,
        inviteeName: json['inviteeName'] as String,
        invitedAt: DateTime.parse(json['invitedAt'] as String),
        status: InviteRequestStatus.values.firstWhere(
          (e) => e.name == json['status'],
          orElse: () => InviteRequestStatus.pending,
        ),
        message: json['message'] as String?,
        respondedAt: json['respondedAt'] != null ? DateTime.parse(json['respondedAt'] as String) : null,
        expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt'] as String) : null,
      ),).toList();
      return Result.success(invites);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get sent invites: $e'));
    }
  }

  @override
  Future<Result<List<BubbleInviteRequest>>> getBubbleInvites(String bubbleId) async {
    try {
      final response = await _remoteDataSource.get('/api/bubbles/$bubbleId/invites');
      final List<dynamic> data = response['invites'] ?? [];
      final invites = data.map((json) => BubbleInviteRequest(
        id: json['id'] as String,
        bubbleId: json['bubbleId'] as String,
        bubbleName: json['bubbleName'] as String,
        inviterId: json['inviterId'] as String,
        inviterName: json['inviterName'] as String,
        inviteeId: json['inviteeId'] as String,
        inviteeName: json['inviteeName'] as String,
        invitedAt: DateTime.parse(json['invitedAt'] as String),
        status: InviteRequestStatus.values.firstWhere(
          (e) => e.name == json['status'],
          orElse: () => InviteRequestStatus.pending,
        ),
        message: json['message'] as String?,
        respondedAt: json['respondedAt'] != null ? DateTime.parse(json['respondedAt'] as String) : null,
        expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt'] as String) : null,
      ),).toList();
      return Result.success(invites);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get bubble invites: $e'));
    }
  }

  @override
  Future<Result<void>> acceptInvite(String inviteId) async {
    try {
      await _remoteDataSource.post('/bubble/requests/$inviteId/accept', {});
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to accept invite: $e'));
    }
  }

  @override
  Future<Result<void>> declineInvite(String inviteId) async {
    try {
      await _remoteDataSource.post('/bubble/requests/$inviteId/decline', {});
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to decline invite: $e'));
    }
  }

  @override
  Future<Result<void>> cancelInvite(String inviteId) async {
    try {
      await _remoteDataSource.delete('/api/invites/$inviteId');
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to cancel invite: $e'));
    }
  }

  @override
  Future<Result<BubbleInviteRequest>> getInvite(String inviteId) async {
    try {
      final response = await _remoteDataSource.get('/api/invites/$inviteId');
      final invite = BubbleInviteRequest(
        id: response['id'] as String,
        bubbleId: response['bubbleId'] as String,
        bubbleName: response['bubbleName'] as String,
        inviterId: response['inviterId'] as String,
        inviterName: response['inviterName'] as String,
        inviteeId: response['inviteeId'],
        inviteeName: response['inviteeName'],
        invitedAt: DateTime.parse(response['invitedAt'] as String),
        status: InviteRequestStatus.values.firstWhere(
          (e) => e.name == response['status'],
          orElse: () => InviteRequestStatus.pending,
        ),
        message: response['message'] as String?,
        respondedAt: response['respondedAt'] != null ? DateTime.parse(response['respondedAt'] as String) : null,
        expiresAt: response['expiresAt'] != null ? DateTime.parse(response['expiresAt'] as String) : null,
      );
      return Result.success(invite);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get invite: $e'));
    }
  }

  @override
  Future<Result<bool>> hasPendingInvite(String bubbleId, String userId) async {
    try {
      final response = await _remoteDataSource.get('/api/bubbles/$bubbleId/pending-invites/$userId');
      return Result.success(response['hasPendingInvite'] as bool? ?? false);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to check pending invite: $e'));
    }
  }

  @override
  Future<Result<BubbleInviteRequest>> resendInvite(String inviteId) async {
    try {
      final response = await _remoteDataSource.post('/api/invites/$inviteId/resend', {});
      final invite = BubbleInviteRequest(
        id: response['id'] as String,
        bubbleId: response['bubbleId'] as String,
        bubbleName: response['bubbleName'] as String,
        inviterId: response['inviterId'] as String,
        inviterName: response['inviterName'] as String,
        inviteeId: response['inviteeId'],
        inviteeName: response['inviteeName'],
        invitedAt: DateTime.parse(response['invitedAt'] as String),
        status: InviteRequestStatus.values.firstWhere(
          (e) => e.name == response['status'],
          orElse: () => InviteRequestStatus.pending,
        ),
        message: response['message'] as String?,
        respondedAt: response['respondedAt'] != null ? DateTime.parse(response['respondedAt'] as String) : null,
        expiresAt: response['expiresAt'] != null ? DateTime.parse(response['expiresAt'] as String) : null,
      );
      return Result.success(invite);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to resend invite: $e'));
    }
  }

  @override
  Future<Result<List<BubbleInviteRequest>>> getInviteHistory(String bubbleId) async {
    try {
      final response = await _remoteDataSource.get('/api/bubbles/$bubbleId/invites/history');
      final List<dynamic> data = response['invites'] ?? [];
      final invites = data.map((json) => BubbleInviteRequest(
        id: json['id'] as String,
        bubbleId: json['bubbleId'] as String,
        bubbleName: json['bubbleName'] as String,
        inviterId: json['inviterId'] as String,
        inviterName: json['inviterName'] as String,
        inviteeId: json['inviteeId'] as String,
        inviteeName: json['inviteeName'] as String,
        invitedAt: DateTime.parse(json['invitedAt'] as String),
        status: InviteRequestStatus.values.firstWhere(
          (e) => e.name == json['status'],
          orElse: () => InviteRequestStatus.pending,
        ),
        message: json['message'] as String?,
        respondedAt: json['respondedAt'] != null ? DateTime.parse(json['respondedAt'] as String) : null,
        expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt'] as String) : null,
      ),).toList();
      return Result.success(invites);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get invite history: $e'));
    }
  }

  @override
  Future<Result<void>> expireOldInvites() async {
    try {
      await _remoteDataSource.post('/api/invites/expire-old', {});
      return Result.success(null);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to expire old invites: $e'));
    }
  }
} 