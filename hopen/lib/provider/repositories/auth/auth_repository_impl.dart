import '../../../repositories/auth/auth_repository.dart';
import '../../../statefulbusinesslogic/core/error/exceptions.dart';
import '../../../statefulbusinesslogic/core/error/result.dart';
import '../../../statefulbusinesslogic/core/models/user_model.dart';
import '../../services/auth/ory_auth_service.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';

class AuthRepositoryImpl implements AuthRepository {

  AuthRepositoryImpl({
    required this.oryAuthService,
  });
  final OryAuthService oryAuthService;
  UserModel? _authenticatedUser;

  @override
  Future<Result<UserModel>> login({
    required String email,
    required String password,
  }) async {
    try {
      // Use OryAuthService for email/password login (Ory Kratos)
      final authResponse = await oryAuthService.signInWithEmail(
        email: email,
        password: password,
      );

      if (authResponse.user != null) {
        final userModel = _convertOryUserToUserModel(authResponse.user!);
        _authenticatedUser = userModel;
        return Result.success(userModel);
      }

      return Result.failure(
        AuthenticationError(message: 'Login returned no user'),
      );
    } on ServerException catch (e) {
      return Result.failure(ValidationError(message: e.message));
    } on NetworkException catch (e) {
      return Result.failure(NetworkError(message: e.message));
    } catch (e) {
      return Result.failure(AuthenticationError(message: 'Login failed: ${e.toString()}'));
    }
  }

  @override
  Future<Result<UserModel>> loginWithGoogle() async {
    try {
      // Let OryAuthService handle Google SDK flow and session creation
      final authResponse = await oryAuthService.signInWithGoogle();

      if (authResponse.user != null) {
        final userModel = _convertOryUserToUserModel(authResponse.user!);
        _authenticatedUser = userModel;
        return Result.success(userModel);
      }

      return Result.failure(
        AuthenticationError(message: 'Google sign-in returned no user'),
      );
    } on ServerException catch (e) {
      return Result.failure(ValidationError(message: e.message));
    } catch (e) {
      return Result.failure(AuthenticationError(message: 'Google sign-in failed: ${e.toString()}'));
    }
  }

  @override
  Future<Result<UserModel>> loginWithApple() async {
    try {
      // Use OryAuthService for Apple Sign-in (native SDK + Kratos)
      final authResponse = await oryAuthService.signInWithApple();

      if (authResponse.user != null) {
        final userModel = _convertOryUserToUserModel(authResponse.user!);
        _authenticatedUser = userModel;
        return Result.success(userModel);
      }

      return Result.failure(
        AuthenticationError(message: 'Apple sign-in returned no user'),
      );
    } on ServerException catch (e) {
      return Result.failure(ValidationError(message: e.message));
    } catch (e) {
      return Result.failure(AuthenticationError(message: 'Apple sign-in failed: ${e.toString()}'));
    }
  }

  @override
  Future<Result<UserModel>> signUp({
    required String email,
    required String password,
    required String username,
    required String firstName,
    required String lastName,
    DateTime? birthday,
    String? profilePictureUrl,
    bool notificationsEnabled = false,
  }) async {
    try {
      // Use OryAuthService for email/password signup (Ory Kratos)
      final authResponse = await oryAuthService.signUpWithEmail(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        username: username, // FIX: Pass the username parameter!
        dateOfBirth: birthday, // FIX: Pass the date of birth parameter!
      );

      if (authResponse.user != null) {
        final userModel = _convertOryUserToUserModel(authResponse.user!);
        _authenticatedUser = userModel;
        return Result.success(userModel);
      }

      return Result.failure(
        AuthenticationError(message: 'Signup returned no user'),
      );
    } on ServerException catch (e) {
      return Result.failure(ValidationError(message: e.message));
    } on NetworkException catch (e) {
      return Result.failure(NetworkError(message: e.message));
    } catch (e) {
      return Result.failure(AuthenticationError(message: 'Sign up failed: ${e.toString()}'));
    }
  }

  @override
  Future<Result<UserModel>> getCurrentUser() async {
    try {
      if (_authenticatedUser != null) {
        return Result.success(_authenticatedUser!);
      }

      // Check if user is signed in via OryAuthService
      if (oryAuthService.isSignedIn && oryAuthService.currentUser != null) {
        final userModel = _convertOryUserToUserModel(oryAuthService.currentUser!);
        _authenticatedUser = userModel;
        return Result.success(userModel);
      }

      return Result.failure(const AuthenticationError(message: 'No user logged in'));
    } on ServerException catch (e) {
      return Result.failure(ValidationError(message: e.message));
    } on NetworkException catch (e) {
      return Result.failure(NetworkError(message: e.message));
    } catch (e) {
      return Result.failure(NetworkError(message: 'Get current user failed: ${e.toString()}'));
    }
  }

  @override
  Future<Result<bool>> logout() async {
    try {
      // Use OryAuthService for logout (Ory Kratos)
      await oryAuthService.signOut();

      // Clear local authentication state
      _authenticatedUser = null;

      return Result.success(true);
    } catch (e) {
      // Even if remote logout fails, clear local state
      _authenticatedUser = null;
      LoggingService.warning('Logout failed but cleared local state: $e');
      return Result.success(true);
    }
  }

  /// Convert Ory user to UserModel
  UserModel _convertOryUserToUserModel(OryUser oryUser) {
    // Extract name components from traits
    final traits = oryUser.traits;
    final firstName = traits['first_name'] as String? ??
                     traits['given_name'] as String? ??
                     oryUser.name?.split(' ').first ?? '';
    final lastName = traits['last_name'] as String? ??
                    traits['family_name'] as String? ??
                    ((oryUser.name?.split(' ').length ?? 0) > 1 ? oryUser.name!.split(' ').last : '');
    final username = traits['username'] as String? ??
                    traits['preferred_username'] as String? ??
                    oryUser.email.split('@')[0];

    return UserModel(
      id: oryUser.id,
      email: oryUser.email,
      username: username,
      firstName: firstName,
      lastName: lastName,
      profilePictureUrl: oryUser.avatarUrl,
      birthday: traits['birthday'] != null ? DateTime.tryParse(traits['birthday'] as String) : null,
      hasCompletedOnboarding: false, // Will be updated separately
    );
  }

  @override
  Future<Result<bool>> updateOnboardingStatus(bool hasCompletedOnboarding) async {
    try {
      // For now, just update local state since Ory Kratos doesn't handle onboarding status
      // This could be extended to call a custom backend endpoint if needed
      if (_authenticatedUser != null) {
        _authenticatedUser = _authenticatedUser!.copyWith(
          hasCompletedOnboarding: hasCompletedOnboarding,
        );
      }
      return Result.success(true);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Update onboarding status failed: ${e.toString()}'));
    }
  }

  @override
  UserModel? get authenticatedUser => _authenticatedUser;

  @override
  bool get isAuthenticated => _authenticatedUser != null;

  /// Clear cached user data to force fresh data retrieval on next getCurrentUser call
  void clearCachedUser() {
    _authenticatedUser = null;
  }
}
