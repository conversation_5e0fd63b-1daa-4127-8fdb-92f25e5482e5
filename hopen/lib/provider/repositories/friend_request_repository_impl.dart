import '../../repositories/friendship/friend_request_repository.dart';
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/friend_request.dart';
import '../../statefulbusinesslogic/core/error/exceptions.dart';
import '../datasources/http_remote_datasource.dart';

class FriendRequestRepositoryImpl implements FriendRequestRepository {
  const FriendRequestRepositoryImpl({
    required HttpRemoteDataSource remoteDataSource,
  }) : _remoteDataSource = remoteDataSource;

  final HttpRemoteDataSource _remoteDataSource;

  @override
  Future<Result<List<FriendRequest>>> getPendingFriendRequests() async {
    try {
      print('🔍 FriendRequestRepositoryImpl.getPendingFriendRequests: fetching pending requests');
      final response = await _remoteDataSource.get('/friendship/requests');
      print('🔍 FriendRequestRepositoryImpl.getPendingFriendRequests: response=$response');
      
      final requests = <FriendRequest>[];
      for (final requestData in response['requests'] ?? []) {
        requests.add(FriendRequest.fromJson(requestData));
      }
      
      print('🔍 FriendRequestRepositoryImpl.getPendingFriendRequests: parsed ${requests.length} requests');
      return Result.success(requests);
    } catch (e) {
      print('🔍 FriendRequestRepositoryImpl.getPendingFriendRequests: error=$e');
      return Result.failure(const NetworkException(message: 'Failed to get pending friend requests'));
    }
  }

  @override
  Future<Result<void>> acceptFriendRequest(String requestId) async {
    try {
      print('🔍 FriendRequestRepositoryImpl.acceptFriendRequest: requestId=$requestId');
      await _remoteDataSource.post('/friendship/requests/$requestId/accept', {});
      print('🔍 FriendRequestRepositoryImpl.acceptFriendRequest: success');
      return Result.success(null);
    } catch (e) {
      print('🔍 FriendRequestRepositoryImpl.acceptFriendRequest: error=$e');
      return Result.failure(const NetworkException(message: 'Failed to accept friend request'));
    }
  }

  @override
  Future<Result<void>> declineFriendRequest(String requestId) async {
    try {
      print('🔍 FriendRequestRepositoryImpl.declineFriendRequest: requestId=$requestId');
      await _remoteDataSource.post('/friendship/requests/$requestId/decline', {});
      print('🔍 FriendRequestRepositoryImpl.declineFriendRequest: success');
      return Result.success(null);
    } catch (e) {
      print('🔍 FriendRequestRepositoryImpl.declineFriendRequest: error=$e');
      return Result.failure(const NetworkException(message: 'Failed to decline friend request'));
    }
  }

  @override
  Future<Result<FriendRequest>> getFriendRequest(String requestId) async {
    try {
      print('🔍 FriendRequestRepositoryImpl.getFriendRequest: requestId=$requestId');
      final response = await _remoteDataSource.get('/friendship/requests/$requestId');
      print('🔍 FriendRequestRepositoryImpl.getFriendRequest: response=$response');
      
      final request = FriendRequest.fromJson(response['request']);
      print('🔍 FriendRequestRepositoryImpl.getFriendRequest: parsed request=${request.toJson()}');
      return Result.success(request);
    } catch (e) {
      print('🔍 FriendRequestRepositoryImpl.getFriendRequest: error=$e');
      return Result.failure(const NetworkException(message: 'Failed to get friend request'));
    }
  }

  @override
  Future<Result<List<String>>> getFriends() async {
    try {
      print('🔍 FriendRequestRepositoryImpl.getFriends: fetching friends list');
      final response = await _remoteDataSource.get('/friendship/friends');
      print('🔍 FriendRequestRepositoryImpl.getFriends: response=$response');
      
      final friends = <String>[];
      for (final friendData in response['friends'] ?? []) {
        if (friendData is Map<String, dynamic> && friendData['user_id'] != null) {
          friends.add(friendData['user_id']);
        } else if (friendData is String) {
          friends.add(friendData);
        }
      }
      
      print('🔍 FriendRequestRepositoryImpl.getFriends: parsed ${friends.length} friends');
      return Result.success(friends);
    } catch (e) {
      print('🔍 FriendRequestRepositoryImpl.getFriends: error=$e');
      return Result.failure(const NetworkException(message: 'Failed to get friends list'));
    }
  }
}
