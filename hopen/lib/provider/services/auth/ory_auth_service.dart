import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import 'package:http/http.dart' show ClientException;
import 'package:http/io_client.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:logger/logger.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:workmanager/workmanager.dart' as wm;
import 'package:background_fetch/background_fetch.dart';

import '../../../config/app_config.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../exceptions/api_exceptions.dart';
import '../http3_client_service.dart';

/// Comprehensive Ory Stack Authentication Service
/// Supports email/password, Google Sign-in, and Apple Sign-in via Ory Kratos and Hydra
class OryAuthService {
  factory OryAuthService() => _instance;

  OryAuthService._internal();
  static final OryAuthService _instance = OryAuthService._internal();

  final Logger _logger = Logger();

  // HTTP client for development SSL handling
  Http3ClientService? _httpClient;

  // Secure storage for session tokens
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  // Google Sign-In configuration
  GoogleSignIn? _googleSignIn;

  // Stream controllers
  final StreamController<OryAuthState> _authStateController = StreamController.broadcast();
  final StreamController<OryUser?> _userController = StreamController.broadcast();

  // State tracking
  bool _isInitialized = false;
  OryUser? _currentUser;
  String? _accessToken;
  Timer? _tokenRefreshTimer;

  // Getters
  Stream<OryAuthState> get authStateStream => _authStateController.stream;
  Stream<OryUser?> get userStream => _userController.stream;
  OryUser? get currentUser => _currentUser;
  String? get accessToken => _accessToken;
  bool get isSignedIn => _currentUser != null && _accessToken != null;
  bool get isInitialized => _isInitialized;

  // HTTP client getter with lazy initialization
  Future<Http3ClientService> get httpClient async {
    if (_httpClient == null) {
      _httpClient = Http3ClientService();
      await _httpClient!.initialize();
    }
    return _httpClient!;
  }

  /// Initialize the Ory authentication service
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.i('Ory Auth Service already initialized');
      return;
    }

    try {
      // Initialize Google Sign-In
      // Note: serverClientId is not supported on web platform
      _googleSignIn = GoogleSignIn(
        scopes: ['openid', 'profile', 'email'],
        // Only set serverClientId for mobile platforms
        serverClientId: kIsWeb ? null : '257996495540-8555p6h5grhi9qmqrts661sro96kfd7.apps.googleusercontent.com',
        // Enhanced configuration for better platform support and security
        hostedDomain: '', // Optional: restrict to specific domains (empty for any domain)
        signInOption: SignInOption.standard, // Use standard sign-in flow for better UX
      );

      // Check for existing session
      print('🔍 OryAuthService.initialize: About to check existing session');
      await _checkExistingSession();
      print('🔍 OryAuthService.initialize: After checking existing session, _accessToken=${_accessToken?.substring(0, 20)}..., _currentUser=${_currentUser?.email}');

      _isInitialized = true;
      _logger.i('Ory Auth Service initialized successfully');

      await _registerBackgroundTasks();

    } catch (e, stackTrace) {
      _logger.e('Failed to initialize Ory Auth Service', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Check for existing session on app start
  Future<void> _checkExistingSession() async {
    try {
      // Try to get session token from secure storage first
      String? storedToken = await _secureStorage.read(key: 'session_token');
      print('🔍 OryAuthService._checkExistingSession: secureStorage token=${storedToken?.substring(0, 20)}...');

      if (storedToken != null) {
        _accessToken = storedToken;
        print('🔍 OryAuthService._checkExistingSession: Set _accessToken from storage');

        // Validate the stored session with Ory Kratos
        try {
          await _validateStoredSession();
          print('🔍 OryAuthService._checkExistingSession: Session validation successful');
        } catch (e) {
          print('🔍 OryAuthService._checkExistingSession: Session validation failed: $e');
          // Clear invalid session
          await _clearSession();
        }
      } else {
        print('🔍 OryAuthService._checkExistingSession: No token found in secure storage');
      }

      // If we have a token (either from memory or storage), validate it
      if (_accessToken != null) {
        print('🔍 OryAuthService._checkExistingSession: Validating token with /sessions/whoami');
        final client = await httpClient;
        final response = await client.get(
          '${AppConfig.oryKratosPublicUrl}/sessions/whoami',
          headers: {
            'Authorization': 'Bearer $_accessToken',
            'Accept': 'application/json',
          },
        );

        print('🔍 OryAuthService._checkExistingSession: /sessions/whoami response: ${response.statusCode}');

        if (response.statusCode == 200) {
          final responseData = json.decode(response.body) as Map<String, dynamic>;
          print('🔍 OryAuthService._checkExistingSession: Full response data: $responseData');
          print('🔍 OryAuthService._checkExistingSession: Identity data: ${responseData['identity']}');
          final identityData = responseData['identity'] as Map<String, dynamic>;
          print('🔍 OryAuthService._checkExistingSession: About to create OryUser from: $identityData');
          _currentUser = OryUser.fromJson(identityData);
          print('🔍 OryAuthService._checkExistingSession: Created OryUser: name=${_currentUser?.name}, email=${_currentUser?.email}');
          print('🔍 OryAuthService._checkExistingSession: OryUser traits: ${_currentUser?.traits}');
          // Note: /sessions/whoami doesn't return session_token, so keep existing token

          _authStateController.add(OryAuthState.signedIn);
          _userController.add(_currentUser);
          _scheduleSessionValidation();

          _logger.i('Existing session restored');
          print('🔍 OryAuthService._checkExistingSession: Session restored successfully');
        } else {
          _logger.d('No existing session found or session expired');
          print('🔍 OryAuthService._checkExistingSession: Session invalid, clearing token');
          // Clear invalid token
          _accessToken = null;
          await _secureStorage.delete(key: 'session_token');
        }
      } else {
        _logger.d('No stored session token found');
        print('🔍 OryAuthService._checkExistingSession: No token found');
      }
    } catch (e) {
      _logger.w('Failed to check existing session: $e');
      print('🔍 OryAuthService._checkExistingSession: ERROR: $e');
      // Clear potentially corrupted token
      _accessToken = null;
      await _secureStorage.delete(key: 'session_token');
    }
  }



  /// Sign up with email and password
  Future<OryAuthResponse> signUpWithEmail({
    required String email,
    required String password,
    String? firstName,
    String? lastName,
    String? username,
    DateTime? dateOfBirth,
    Map<String, dynamic>? metadata,
  }) async {
    _ensureInitialized();

    try {
      _logger.i('Attempting to sign up with email: $email');

      // Call backend registration endpoint instead of Kratos directly
      // This ensures both Kratos identity and local database user are created
      final client = await httpClient;
      final registrationData = <String, dynamic>{
        'email': email,
        'password': password,
        'username': username ?? email.split('@')[0],
        'first_name': firstName ?? email.split('@')[0],
        'last_name': lastName ?? 'User',
      };

      // Add date of birth if provided
      if (dateOfBirth != null) {
        registrationData['date_of_birth'] = dateOfBirth.toIso8601String().split('T')[0]; // Format as YYYY-MM-DD
      }

      _logger.i('Sending registration data to backend: ${json.encode(registrationData)}');

      final response = await client.post(
        '${AppConfig.apiBaseUrl}/api/v1/auth/register',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: registrationData,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = json.decode(response.body) as Map<String, dynamic>;

        // Handle backend registration response format
        if (responseData['success'] == true && responseData['user'] != null) {
          final userData = responseData['user'] as Map<String, dynamic>;

          // Create OryUser from backend user data
          final traits = <String, dynamic>{
            'email': userData['email'] as String,
            'username': userData['username'] as String,
            'first_name': userData['first_name'] as String,
            'last_name': userData['last_name'] as String,
          };

          _currentUser = OryUser(
            id: userData['id'] as String,
            email: userData['email'] as String,
            name: '${userData['first_name']} ${userData['last_name']}',
            avatarUrl: null, // Will be set later if profile picture exists
            traits: traits,
            createdAt: DateTime.now(), // Backend doesn't return these, use current time
            updatedAt: DateTime.now(),
          );

          // Get session token from backend response
          _accessToken = responseData['access_token'] as String?;

          // Store the session token securely
          if (_accessToken != null) {
            await _secureStorage.write(key: 'session_token', value: _accessToken!);
          }

          _authStateController.add(OryAuthState.signedIn);
          _userController.add(_currentUser);
          _scheduleSessionValidation();

          _logger.i('Sign up successful for email: $email');
          return OryAuthResponse(user: _currentUser, sessionToken: _accessToken);
        } else {
          // Handle partial success (account created but authentication failed)
          throw AuthenticationException(
            message: responseData['message'] as String? ?? 'Registration completed but authentication failed',
          );
        }
      } else {
        // Improved error handling with detailed response logging
        var errorMessage = 'Sign up failed';
        
        _logger.e('Sign up failed with status ${response.statusCode}');
        _logger.e('Response body: ${response.body}');
        
        try {
          final errorData = json.decode(response.body) as Map<String, dynamic>;
          
          // Check various possible error structures
          if (errorData['ui'] != null && errorData['ui']['messages'] != null) {
            final messages = errorData['ui']['messages'] as List?;
            if (messages != null && messages.isNotEmpty) {
              errorMessage = messages.first['text'] as String? ?? errorMessage;
            }
          } else if (errorData['error'] != null) {
            if (errorData['error'] is String) {
              errorMessage = errorData['error'] as String;
            } else if (errorData['error']['message'] != null) {
              errorMessage = errorData['error']['message'] as String;
            }
          } else if (errorData['message'] != null) {
            errorMessage = errorData['message'] as String;
          }
          
          _logger.e('Parsed error message: $errorMessage');
        } catch (e) {
          _logger.w('Failed to parse error response: $e');
          errorMessage = 'Sign up failed with status ${response.statusCode}. Response: ${response.body.substring(0, 200)}...';
        }
        
        throw AuthenticationException(message: errorMessage);
      }
    } catch (e, stackTrace) {
      _logger.e('Sign up error', error: e, stackTrace: stackTrace);
      
      // Log detailed error information for debugging
      var detailedError = 'Sign up failed: $e';
      if (e is SocketException) {
        detailedError = 'Network connection failed: ${e.message}. Please check if the backend is running and accessible.';
      } else if (e is ClientException) {
        detailedError = 'HTTP client error: ${e.message}. Backend connection issue.';
      } else if (e is FormatException) {
        detailedError = 'Response parsing error: ${e.message}. Backend returned invalid format.';
      }
      
      _logger.e('Detailed sign up error: $detailedError');
      
      if (e is AuthenticationException) rethrow;
      throw AuthenticationException(message: detailedError);
    }
  }

  /// Sign in with email and password
  Future<OryAuthResponse> signInWithEmail({
    required String email,
    required String password,
  }) async {
    _ensureInitialized();

    try {
      _logger.i('Attempting to sign in with email: $email');



      // Step 1: Initialize login flow
      final client = await httpClient;
      final flowResponse = await client.get(
        '${AppConfig.oryKratosPublicUrl}/self-service/login/api',
        headers: {'Accept': 'application/json'},
      );

      _logger.i('Flow initialization response status: ${flowResponse.statusCode}');
      _logger.i('Flow initialization response body: ${flowResponse.body}');

      if (flowResponse.statusCode != 200) {
        throw AuthenticationException(message: 'Failed to initialize login flow: ${flowResponse.statusCode}');
      }

      final flowData = json.decode(flowResponse.body) as Map<String, dynamic>;
      final flowId = flowData['id'] as String;

      _logger.i('Login flow ID: $flowId');

      // Extract CSRF token from the flow
      String? csrfToken;
      if (flowData['ui'] != null && flowData['ui']['nodes'] != null) {
        final nodes = flowData['ui']['nodes'] as List;
        for (final node in nodes) {
          if (node['attributes'] != null && 
              node['attributes']['name'] == 'csrf_token') {
            csrfToken = node['attributes']['value'] as String?;
            break;
          }
        }
      }

      _logger.i('CSRF token: $csrfToken');

      // Step 2: Submit login
      final loginData = <String, dynamic>{
        'method': 'password',
        'password': password,
        'identifier': email,
      };

      // Add CSRF token if available
      if (csrfToken != null && csrfToken.isNotEmpty) {
        loginData['csrf_token'] = csrfToken;
      }

      _logger.i('Submitting login data for email: $email');

      final response = await client.post(
        '${AppConfig.oryKratosPublicUrl}/self-service/login?flow=$flowId',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: loginData,
      );

      _logger.i('Login submission response status: ${response.statusCode}');
      _logger.i('Login submission response body: ${response.body}');
      print('🔍 OryAuthService.signInWithEmail: Login response status: ${response.statusCode}');
      print('🔍 OryAuthService.signInWithEmail: Login response body: ${response.body}');

      if (response.statusCode == 200) {
        _logger.i('Login response status: ${response.statusCode}');
        _logger.i('Login response body: ${response.body}');
        print('🔍 OryAuthService.signInWithEmail: Processing successful login response');

        if (response.body.isEmpty) {
          print('🔍 OryAuthService.signInWithEmail: ERROR - Empty response body');
          throw const AuthenticationException(message: 'Empty response from authentication server');
        }

        final responseData = json.decode(response.body) as Map<String, dynamic>;
        print('🔍 OryAuthService.signInWithEmail: Parsed response data: $responseData');

        if (responseData['session'] == null) {
          print('🔍 OryAuthService.signInWithEmail: ERROR - No session in response');
          throw const AuthenticationException(message: 'No session in authentication response');
        }

        if (responseData['session']['identity'] == null) {
          print('🔍 OryAuthService.signInWithEmail: ERROR - No identity in session');
          throw const AuthenticationException(message: 'No user identity in authentication response');
        }

        if (responseData['session_token'] == null) {
          print('🔍 OryAuthService.signInWithEmail: ERROR - No session_token in response');
          throw const AuthenticationException(message: 'No session token in authentication response');
        }

        print('🔍 OryAuthService.signInWithEmail: All validation passed, extracting user and token');
        _currentUser = OryUser.fromJson(responseData['session']['identity'] as Map<String, dynamic>);
        _accessToken = responseData['session_token'] as String?;
        print('🔍 OryAuthService.signInWithEmail: Extracted _accessToken: ${_accessToken?.substring(0, 20)}...');

        // Store the session token securely
        if (_accessToken != null) {
          print('🔍 OryAuthService.signInWithEmail: Storing session token securely');
          await _secureStorage.write(key: 'session_token', value: _accessToken!);
          print('🔍 OryAuthService.signInWithEmail: Session token stored successfully');
        } else {
          print('🔍 OryAuthService.signInWithEmail: WARNING - _accessToken is null, not storing');
        }

        _authStateController.add(OryAuthState.signedIn);
        _userController.add(_currentUser);
        _scheduleSessionValidation();

        _logger.i('Sign in successful for email: $email');
        print('🔍 OryAuthService.signInWithEmail: Sign in completed successfully, user: ${_currentUser?.email}');
        return OryAuthResponse(user: _currentUser, sessionToken: _accessToken);
      } else {
        // Improved error handling
        var errorMessage = 'Sign in failed';
        try {
          final errorData = json.decode(response.body) as Map<String, dynamic>;
          
          // Check various possible error structures
          if (errorData['ui'] != null && errorData['ui']['messages'] != null) {
            final messages = errorData['ui']['messages'] as List?;
            if (messages != null && messages.isNotEmpty) {
              errorMessage = messages.first['text'] as String? ?? errorMessage;
            }
          } else if (errorData['error'] != null) {
            if (errorData['error'] is String) {
              errorMessage = errorData['error'] as String;
            } else if (errorData['error']['message'] != null) {
              errorMessage = errorData['error']['message'] as String;
            }
          } else if (errorData['message'] != null) {
            errorMessage = errorData['message'] as String;
          }
        } catch (e) {
          _logger.w('Failed to parse error response: $e');
          errorMessage = 'Sign in failed with status ${response.statusCode}';
        }
        
        throw AuthenticationException(message: errorMessage);
      }
    } catch (e, stackTrace) {
      _logger.e('Sign in error', error: e, stackTrace: stackTrace);
      if (e is AuthenticationException) rethrow;
      throw AuthenticationException(message: 'Sign in failed: $e');
    }
  }

  /// Sign in with Google using Google SDK (Ory recommended approach for mobile)
  Future<OryAuthResponse> signInWithGoogle() async {
    _ensureInitialized();

    if (_googleSignIn == null) {
      throw const AuthenticationException(message: 'Google Sign-In not configured');
    }

    try {
      _logger.i('Attempting Google Sign-In using Google SDK');

      // Step 1: Use Google SDK to get ID token
      final googleUser = await _googleSignIn!.signIn();
      if (googleUser == null) {
        throw const AuthenticationException(message: 'Google Sign-In was cancelled');
      }

      final googleAuth = await googleUser.authentication;
      
      if (googleAuth.idToken == null) {
        throw const AuthenticationException(message: 'Failed to get Google ID token');
      }

      _logger.i('Successfully obtained Google ID token');

      // Step 2: Initialize login flow with Ory Kratos
      final client = await httpClient;
      final flowResponse = await client.get(
        '${AppConfig.oryKratosPublicUrl}/self-service/login/api',
        headers: {'Accept': 'application/json'},
      );

      if (flowResponse.statusCode != 200) {
        throw const AuthenticationException(message: 'Failed to initialize login flow');
      }

      final flowData = json.decode(flowResponse.body) as Map<String, dynamic>;
      final flowId = flowData['id'] as String;

      _logger.i('Initialized login flow with ID: $flowId');

      // Step 3: Submit Google ID token to Ory Kratos
      final loginData = <String, dynamic>{
        'method': 'oidc',
        'provider': 'google',
        'id_token': googleAuth.idToken,
        // Generate and use nonce for security (prevents replay attacks)
        'id_token_nonce': _generateNonce(),
      };

      final response = await client.post(
        '${AppConfig.oryKratosPublicUrl}/self-service/login?flow=$flowId',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: loginData,
      );

      _logger.i('Submitted Google ID token to Ory, status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body) as Map<String, dynamic>;
        
        // Check if we have session data
        if (responseData['session'] != null && responseData['session_token'] != null) {
          _currentUser = OryUser.fromJson(responseData['session']['identity'] as Map<String, dynamic>);
          _accessToken = responseData['session_token'] as String?;

          // Store the session token securely
          if (_accessToken != null) {
            await _secureStorage.write(key: 'session_token', value: _accessToken!);
          }

          _authStateController.add(OryAuthState.signedIn);
          _userController.add(_currentUser);
          _scheduleSessionValidation();

          _logger.i('Google Sign-In successful for user: ${_currentUser?.email}');
          return OryAuthResponse(user: _currentUser, sessionToken: _accessToken);
        } else {
          throw const AuthenticationException(
            message: 'Login successful but no session created. Please check Ory configuration.',
          );
        }
      } else if (response.statusCode == 422) {
        // Handle validation errors
        final errorData = json.decode(response.body) as Map<String, dynamic>;
        var errorMessage = 'Google Sign-In validation failed';
        
        if (errorData['ui'] != null && errorData['ui']['messages'] != null) {
          final messages = errorData['ui']['messages'] as List?;
          if (messages != null && messages.isNotEmpty) {
            errorMessage = messages.first['text'] as String? ?? errorMessage;
          }
        }
        
        throw AuthenticationException(message: errorMessage);
      } else {
        // Handle other errors
        var errorMessage = 'Google Sign-In failed';
        try {
          final errorData = json.decode(response.body) as Map<String, dynamic>;
          
          if (errorData['ui'] != null && errorData['ui']['messages'] != null) {
            final messages = errorData['ui']['messages'] as List?;
            if (messages != null && messages.isNotEmpty) {
              errorMessage = messages.first['text'] as String? ?? errorMessage;
            }
          } else if (errorData['error'] != null) {
            if (errorData['error'] is String) {
              errorMessage = errorData['error'] as String;
            } else if (errorData['error']['message'] != null) {
              errorMessage = errorData['error']['message'] as String;
            }
          } else if (errorData['message'] != null) {
            errorMessage = errorData['message'] as String;
          }
        } catch (e) {
          _logger.w('Failed to parse error response: $e');
          errorMessage = 'Google Sign-In failed with status ${response.statusCode}';
        }
        
        throw AuthenticationException(message: errorMessage);
      }
    } catch (e, stackTrace) {
      _logger.e('Google Sign-In error', error: e, stackTrace: stackTrace);
      
      // Sign out from Google if there was an error
      if (_googleSignIn != null) {
        try {
          await _googleSignIn!.signOut();
        } catch (signOutError) {
          _logger.w('Failed to sign out from Google: $signOutError');
        }
      }
      
      // Platform-specific error handling for better user experience
      String errorMessage = 'Google Sign-In failed';
      if (e.toString().contains('network_error') || e.toString().contains('network')) {
        errorMessage = Platform.isIOS 
            ? 'Network error. Please check your internet connection and try again.'
            : 'Network error. Please check your internet connection and try again.';
      } else if (e.toString().contains('cancelled') || e.toString().contains('canceled')) {
        errorMessage = 'Sign-in was cancelled';
      } else if (e.toString().contains('popup_closed')) {
        errorMessage = 'Sign-in window was closed. Please try again.';
      } else if (e.toString().contains('popup_blocked')) {
        errorMessage = 'Sign-in popup was blocked. Please allow popups and try again.';
      }
      
      if (e is AuthenticationException) rethrow;
      throw AuthenticationException(message: errorMessage);
    }
  }

  /// Sign in with Apple
  Future<OryAuthResponse> signInWithApple() async {
    _ensureInitialized();

    try {
      _logger.i('Attempting Apple Sign-In');

      // Step 1: Generate nonce for security (prevents replay attacks)
      final rawNonce = _generateNonce();
      final nonce = _sha256ofString(rawNonce);

      // Step 2: Get Apple ID credential using native SDK with nonce
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: nonce, // Add nonce for enhanced security
      );

      if (credential.identityToken == null) {
        throw const AuthenticationException(message: 'Failed to get Apple identity token');
      }

      _logger.d('Apple Sign-In successful, got identity token with nonce validation');

      // Step 2: Initialize login flow with Ory Kratos
      final client = await httpClient;
      final flowResponse = await client.get(
        '${AppConfig.oryKratosPublicUrl}/self-service/login/api',
        headers: {'Accept': 'application/json'},
      );

      if (flowResponse.statusCode != 200) {
        _logger.e('Failed to initialize login flow: ${flowResponse.statusCode} - ${flowResponse.body}');
        throw const AuthenticationException(message: 'Failed to initialize OAuth flow');
      }

      final flowData = json.decode(flowResponse.body) as Map<String, dynamic>;
      final flowId = flowData['id'] as String;
      _logger.d('Initialized login flow with ID: $flowId');

      // Step 3: Submit Apple ID token to Ory using OIDC method
      final loginData = <String, dynamic>{
        'method': 'oidc',
        'provider': 'apple',
        'id_token': credential.identityToken,
      };

      // Add authorization code if available (Apple provides this)
      loginData['authorization_code'] = credential.authorizationCode;
    
      _logger.d('Submitting Apple OIDC login to Ory');

      final response = await client.post(
        '${AppConfig.oryKratosPublicUrl}/self-service/login?flow=$flowId',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: loginData,
      );

      _logger.d('Ory login response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body) as Map<String, dynamic>;
        
        // Extract user and session information
        _currentUser = OryUser.fromJson(responseData['identity'] as Map<String, dynamic>);
        _accessToken = responseData['session_token'] as String?;
        
        // Update state
        _authStateController.add(OryAuthState.signedIn);
        _userController.add(_currentUser);
        _scheduleSessionValidation();
        
        _logger.i('Apple Sign-In completed successfully');
        return OryAuthResponse(user: _currentUser, sessionToken: _accessToken);
        
      } else {
        // Handle authentication errors
        _logger.e('Apple OIDC login failed: ${response.statusCode} - ${response.body}');
        
        final responseBody = response.body;
        var errorMessage = 'Apple Sign-In failed';
        
        try {
          final errorData = json.decode(responseBody) as Map<String, dynamic>;
          if (errorData['ui'] != null && errorData['ui']['messages'] != null) {
            final messages = errorData['ui']['messages'] as List;
            if (messages.isNotEmpty) {
              errorMessage = messages.first['text'] as String? ?? errorMessage;
            }
          } else if (errorData['error'] != null) {
            errorMessage = errorData['error']['message'] as String? ?? errorMessage;
          }
        } catch (e) {
          _logger.w('Failed to parse error response: $e');
        }
        
        throw AuthenticationException(message: errorMessage);
      }
    } catch (e, stackTrace) {
      _logger.e('Apple Sign-In error', error: e, stackTrace: stackTrace);
      
      // Platform-specific error handling for better user experience
      String errorMessage = 'Apple Sign-In failed';
      if (e.toString().contains('network_error') || e.toString().contains('network')) {
        errorMessage = Platform.isIOS 
            ? 'Network error. Please check your internet connection and try again.'
            : 'Network error. Please check your internet connection and try again.';
      } else if (e.toString().contains('cancelled') || e.toString().contains('canceled')) {
        errorMessage = 'Sign-in was cancelled';
      } else if (e.toString().contains('not_available')) {
        errorMessage = Platform.isIOS 
            ? 'Apple Sign-In is not available on this device. Please use email/password or Google Sign-In.'
            : 'Apple Sign-In is not available on this platform. Please use email/password or Google Sign-In.';
      } else if (e.toString().contains('invalid_credentials')) {
        errorMessage = 'Invalid Apple ID credentials. Please try again.';
      }
      
      if (e is AuthenticationException) rethrow;
      throw AuthenticationException(message: errorMessage);
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      if (_accessToken != null) {
        // Revoke session with Ory Kratos
        final client = await httpClient;
        await client.delete(
          '${AppConfig.oryKratosPublicUrl}/sessions',
          headers: {
            'Authorization': 'Bearer $_accessToken',
            'Accept': 'application/json',
          },
        );
      }

      // Sign out from Google if signed in
      if (_googleSignIn != null) {
        await _googleSignIn!.signOut();
      }

      _currentUser = null;
      _accessToken = null;
      _tokenRefreshTimer?.cancel();
      
      _authStateController.add(OryAuthState.signedOut);
      _userController.add(null);
      
      _logger.i('User signed out successfully');
    } catch (e, stackTrace) {
      _logger.e('Sign out error', error: e, stackTrace: stackTrace);
      // Still clear local state even if server call fails
      _currentUser = null;
      _accessToken = null;
      _tokenRefreshTimer?.cancel();
      
      _authStateController.add(OryAuthState.signedOut);
      _userController.add(null);
    }
  }

  /// Schedule periodic session validation
  void _scheduleSessionValidation() {
    _tokenRefreshTimer?.cancel();

    if (_accessToken != null) {
      // For Ory Kratos, we don't need to refresh tokens like JWT
      // Instead, we can periodically validate the session
      // Schedule validation every 30 minutes
      _tokenRefreshTimer = Timer.periodic(
        const Duration(minutes: 30),
        (_) => _validateSession(),
      );
      _logger.i('📅 Session validation scheduled every 30 minutes');
    }
  }

  /// Validate current session using Ory Kratos whoami endpoint
  Future<void> _validateSession() async {
    try {
      final client = await httpClient;
      final response = await client.get(
        '${AppConfig.oryKratosPublicUrl}/sessions/whoami',
        headers: {
          'Authorization': 'Bearer $_accessToken',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        // Session is valid, no action needed
        _logger.i('✅ Session validated successfully');
      } else {
        _logger.w('⚠️ Session validation failed, signing out user');
        await signOut();
      }
    } catch (e) {
      _logger.e('⚠️ Session validation failed: $e');
      await signOut();
    }
  }

  /// Refresh current user data from Ory (public method)
  Future<void> refreshUserData() async {
    try {
      if (_accessToken == null) {
        _logger.w('No access token available for refreshing user data');
        return;
      }

      print('🔍 OryAuthService.refreshUserData: Refreshing user data from Ory...');

      final client = await httpClient;
      final response = await client.get(
        '${AppConfig.oryKratosPublicUrl}/sessions/whoami',
        headers: {
          'Accept': 'application/json',
          'X-Session-Token': _accessToken!,
        },
      );

      print('🔍 OryAuthService.refreshUserData: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body) as Map<String, dynamic>;
        final identityData = responseData['identity'] as Map<String, dynamic>;

        // Update current user with fresh data from Ory
        _currentUser = OryUser.fromJson(identityData);

        // Update state streams to notify listeners
        _userController.add(_currentUser);

        print('🔍 OryAuthService.refreshUserData: User data refreshed successfully for: ${_currentUser?.email}');
        _logger.i('✅ User data refreshed successfully');
      } else {
        print('🔍 OryAuthService.refreshUserData: Failed to refresh user data, status: ${response.statusCode}');
        _logger.w('⚠️ Failed to refresh user data: ${response.statusCode}');
      }
    } catch (e) {
      print('🔍 OryAuthService.refreshUserData: ERROR: $e');
      _logger.e('⚠️ Failed to refresh user data: $e');
    }
  }

  /// Ensure service is initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw Exception('OryAuthService not initialized. Call initialize() first.');
    }
  }

  /// Dispose resources
  void dispose() {
    _tokenRefreshTimer?.cancel();
    _authStateController.close();
    _userController.close();
  }

  /// Debug method to check what's in storage
  Future<void> debugTokenStorage() async {
    try {
      final storedToken = await _secureStorage.read(key: 'session_token');
      print('🔍 DEBUG: Token in storage: $storedToken');
      print('🔍 DEBUG: Token in memory (_accessToken): $_accessToken');
      print('🔍 DEBUG: Current user: $_currentUser');
      print('🔍 DEBUG: Is signed in: $isSignedIn');
    } catch (e) {
      print('🔍 DEBUG: Error reading storage: $e');
    }
  }

  /// Store session token (for external integration)
  Future<void> storeSessionToken(String token) async {
    try {
      _accessToken = token;
      await _secureStorage.write(key: 'session_token', value: token);
      print('🔍 OryAuthService.storeSessionToken: Stored token: ${token.substring(0, 20)}...');
    } catch (e) {
      LoggingService.error('Failed to store session token: $e');
    }
  }

  /// Validate stored session and restore user state
  Future<void> _validateStoredSession() async {
    if (_accessToken == null) {
      throw const AuthenticationException(message: 'No stored token to validate');
    }

    print('🔍 OryAuthService._validateStoredSession: Validating stored session...');

    final client = await httpClient;
    final response = await client.get(
      '${AppConfig.oryKratosPublicUrl}/sessions/whoami',
      headers: {
        'Accept': 'application/json',
        'X-Session-Token': _accessToken!,
      },
    );

    print('🔍 OryAuthService._validateStoredSession: Response status: ${response.statusCode}');

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body) as Map<String, dynamic>;
      final identityData = responseData['identity'] as Map<String, dynamic>;

      // Restore user state
      _currentUser = OryUser.fromJson(identityData);

      // Update state streams
      _authStateController.add(OryAuthState.signedIn);
      _userController.add(_currentUser);

      print('🔍 OryAuthService._validateStoredSession: Session restored for user: ${_currentUser?.email}');
    } else {
      print('🔍 OryAuthService._validateStoredSession: Session validation failed with status: ${response.statusCode}');
      throw AuthenticationException(message: 'Session validation failed: ${response.statusCode}');
    }
  }

  /// Clear session data
  Future<void> _clearSession() async {
    try {
      _accessToken = null;
      _currentUser = null;
      await _secureStorage.delete(key: 'session_token');

      _authStateController.add(OryAuthState.signedOut);
      _userController.add(null);

      print('🔍 OryAuthService._clearSession: Session cleared');
    } catch (e) {
      LoggingService.error('Failed to clear session: $e');
    }
  }

  /// Get a valid JWT token for API authentication

  /// Check if a token is expired
  bool _isTokenExpired(String token) {
    try {
      // For JWT tokens, we could decode and check the exp claim
      // For now, we'll implement a simple check based on when we received the token
      // This is a simplified implementation - in production you'd want to decode the JWT

      // If we don't have a session expiry time, assume it's valid for now
      // The server will reject it if it's actually expired
      return false;
    } catch (e) {
      LoggingService.error('Error checking token expiry', error: e);
      // If we can't determine, assume it's expired to be safe
      return true;
    }
  }

  Future<String?> getValidToken() async {
    try {
      print('🔍 OryAuthService.getValidToken: Starting, _accessToken=${_accessToken?.substring(0, 20)}...');

      if (_accessToken == null) {
        print('🔍 OryAuthService.getValidToken: _accessToken is null, calling _checkExistingSession');
        await _checkExistingSession();
        print('🔍 OryAuthService.getValidToken: After _checkExistingSession, _accessToken=${_accessToken?.substring(0, 20)}...');
      }

      if (_accessToken != null) {
        print('🔍 OryAuthService.getValidToken: _accessToken exists, length=${_accessToken!.length}');
        print('🔍 OryAuthService.getValidToken: Full token: $_accessToken');

        // Return the token directly - session validation will be handled separately
        print('🔍 OryAuthService.getValidToken: Returning token');
        return _accessToken;
      }

      print('🔍 OryAuthService.getValidToken: No valid token available, returning null');
      return null;
    } catch (e) {
      LoggingService.error('Failed to get valid token', error: e);
      print('🔍 OryAuthService.getValidToken: ERROR: $e');
      return null;
    }
  }

  /// Initiate password reset flow
  Future<OryAuthResponse> initiatePasswordReset(String email) async {
    _ensureInitialized();

    try {
      _logger.i('Initiating password reset for email: $email');

      // Step 1: Initialize recovery flow
      final client = await httpClient;
      final flowResponse = await client.get(
        '${AppConfig.oryKratosPublicUrl}/self-service/recovery/api',
        headers: {'Accept': 'application/json'},
      );

      if (flowResponse.statusCode != 200) {
        throw AuthenticationException(message: 'Failed to initialize recovery flow: ${flowResponse.statusCode}');
      }

      final flowData = json.decode(flowResponse.body) as Map<String, dynamic>;
      final flowId = flowData['id'] as String;

      // Step 2: Submit recovery request
      final recoveryData = {
        'method': 'link',
        'email': email,
      };

      final response = await client.post(
        '${AppConfig.oryKratosPublicUrl}/self-service/recovery?flow=$flowId',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: recoveryData,
      );

      _logger.i('Recovery response status: ${response.statusCode}');
      _logger.i('Recovery response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 422) {
        // 422 is expected for recovery flows - it means the flow was processed
        _logger.i('Password reset initiated successfully for email: $email');
        return OryAuthResponse(isSuccess: true);
      } else {
        final errorData = json.decode(response.body) as Map<String, dynamic>;
        final errorMessage = errorData['ui']?['messages']?[0]?['text'] as String? ?? 'Password reset failed';
        throw AuthenticationException(message: errorMessage);
      }
    } catch (e, stackTrace) {
      _logger.e('Password reset error', error: e, stackTrace: stackTrace);
      if (e is AuthenticationException) rethrow;
      throw AuthenticationException(message: 'Password reset failed: $e');
    }
  }

  /// Generate a cryptographically secure nonce for OIDC flows
  /// This prevents replay attacks by ensuring each authentication request is unique
  String _generateNonce() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return sha256.convert(bytes).toString();
  }

  /// Generate SHA256 hash of a string for Apple Sign-In nonce validation
  /// This is required for Apple Sign-In security best practices
  String _sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Register platform-specific background tasks for token refresh
  Future<void> _registerBackgroundTasks() async {
    try {
      if (Platform.isAndroid) {
        // WorkManager registration (Android)
        const taskName = 'hopen_token_refresh';

        await wm.Workmanager().initialize(
          _workmanagerCallbackDispatcher,
          isInDebugMode: kDebugMode,
        );

        await wm.Workmanager().registerPeriodicTask(
          taskName,
          taskName,
          frequency: const Duration(hours: 2),
          // Give the app some slack to run after boot or update
          initialDelay: const Duration(minutes: 15),
          constraints: wm.Constraints(
            networkType: wm.NetworkType.connected,
          ),
        );
        _logger.i('WorkManager background task registered');
      } else if (Platform.isIOS || Platform.isMacOS) {
        // BackgroundFetch registration (iOS / macOS)
        await BackgroundFetch.configure(
          BackgroundFetchConfig(
            minimumFetchInterval: 120, // minutes
            stopOnTerminate: false,
            enableHeadless: true,
          ),
          _backgroundFetchHandler,
        );
        _logger.i('BackgroundFetch configured for iOS/macOS');
      }
    } catch (e, st) {
      _logger.w('Background task registration not available: $e', stackTrace: st);
    }
  }

  // ────────────────────────────────────────────────────────────
  // Background task handlers
  // ────────────────────────────────────────────────────────────

  static void _backgroundFetchHandler(String taskId) async {
    try {
      await OryAuthService()._validateSession();
    } finally {
      BackgroundFetch.finish(taskId);
    }
  }

  // ────────────────────────────────────────────────────────────
  // PKCE / OAuth helper utilities
  // ────────────────────────────────────────────────────────────

  /// Generates a high-entropy code verifier (43-128 chars) per RFC 7636.
  String _generateCodeVerifier() {
    final rand = Random.secure();
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    return List.generate(96, (_) => charset[rand.nextInt(charset.length)]).join();
  }

  /// Generates an S256 code challenge from the given [verifier].
  String _codeChallengeS256(String verifier) {
    final bytes = utf8.encode(verifier);
    final digest = sha256.convert(bytes);
    return base64UrlEncode(digest.bytes).replaceAll('=', '');
  }
}

/// Ory authentication state
enum OryAuthState {
  signedIn,
  signedOut,
  loading,
}

/// Ory user model
class OryUser {

  OryUser({
    required this.id,
    required this.email,
    required this.traits, required this.createdAt, required this.updatedAt, this.name,
    this.avatarUrl,
  });

  factory OryUser.fromJson(Map<String, dynamic> json) {
    final traits = json['traits'] as Map<String, dynamic>;

    print('🔍 OryUser.fromJson: traits=$traits');

    // Extract name from various possible fields
    String? name = traits['name'] as String? ??
                   traits['given_name'] as String? ??
                   traits['first_name'] as String? ??
                   traits['family_name'] as String? ??
                   traits['last_name'] as String?;

    print('🔍 OryUser.fromJson: extracted name=$name');

    return OryUser(
      id: json['id'] as String,
      email: traits['email'] as String? ?? '',
      name: name,
      avatarUrl: traits['picture'] as String?,
      traits: traits,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }
  final String id;
  final String email;
  final String? name;
  final String? avatarUrl;
  final Map<String, dynamic> traits;
  final DateTime createdAt;
  final DateTime updatedAt;

  Map<String, dynamic> toJson() => {
      'id': id,
      'email': email,
      'name': name,
      'avatar_url': avatarUrl,
      'traits': traits,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
}

/// Ory authentication response
class OryAuthResponse {

  OryAuthResponse({
    this.user,
    this.sessionToken,
    this.error,
    this.isSuccess,
  });
  final OryUser? user;
  final String? sessionToken;
  final String? error;
  final bool? isSuccess;

  bool get success => isSuccess ?? (user != null && sessionToken != null);
}

// ──────────────────────────────────────────────────────────────
// Workmanager callback (must be top-level)
// ──────────────────────────────────────────────────────────────

@pragma('vm:entry-point')
void _workmanagerCallbackDispatcher() {
  wm.Workmanager().executeTask((taskName, inputData) async {
    if (taskName == 'hopen_session_validation') {
      try {
        await OryAuthService()._validateSession();
        return true;
      } catch (_) {
        return false;
      }
    }
    return true;
  });
}