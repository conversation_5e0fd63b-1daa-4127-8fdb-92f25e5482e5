import 'dart:async';
import '../../statefulbusinesslogic/core/models/user_model.dart';
import '../local_storage/request_state_cache.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';
import '../services/api/http_api_service.dart';

/// Comprehensive request state manager that handles all types of requests
/// and provides persistent state tracking across app restarts.
/// 
/// This service acts as the central hub for request state management,
/// coordinating between the enhanced profile service, local cache,
/// and real-time updates.
/// 
/// Features:
/// - Unified interface for all request types
/// - Automatic state synchronization
/// - Persistent storage with cache management
/// - Real-time state updates
/// - Error handling and recovery
/// - Request validation and conflict resolution
class RequestStateManager {
  RequestStateManager({
    required HttpApiService apiService,
    RequestStateCache? cache,
  }) : _apiService = apiService,
       _cache = cache ?? RequestStateCache();

  final HttpApiService _apiService;
  final RequestStateCache _cache;
  
  // Stream controllers for real-time updates
  final _requestStateController = StreamController<RequestStateUpdate>.broadcast();
  
  /// Stream of request state updates
  Stream<RequestStateUpdate> get requestStateUpdates => _requestStateController.stream;

  /// Initializes the request state manager for a user.
  /// 
  /// This method loads the enhanced profile and synchronizes with cached data.
  /// 
  /// [userId] - The user ID to initialize for
  /// [forceRefresh] - Whether to force a refresh from the server
  Future<UserModel?> initializeForUser({
    required String userId,
    bool forceRefresh = false,
  }) async {
    try {
      LoggingService.info('Initializing request state manager for user $userId');

      // Try to get cached data first if not forcing refresh
      UserModel? userModel;
      if (!forceRefresh) {
        userModel = await _loadFromCache(userId);
      }

      // If no cached data or forcing refresh, load from server
      if (userModel == null || forceRefresh) {
        userModel = await _loadFromServer(userId);
        if (userModel != null) {
          await _cache.cacheRequestStates(userModel: userModel);
        }
      }

      if (userModel != null) {
        LoggingService.info('Request state manager initialized successfully');
        _emitStateUpdate(RequestStateUpdate(
          userId: userId,
          updateType: RequestUpdateType.initialization,
          userModel: userModel,
        ));
      }

      return userModel;
    } catch (e) {
      LoggingService.error('Failed to initialize request state manager: $e');
      return null;
    }
  }

  /// Checks if there's a pending request between two users.
  /// 
  /// This method checks all relevant request types and directions.
  /// 
  /// [currentUserId] - The current user's ID
  /// [targetUserId] - The target user's ID
  /// [requestType] - Optional specific request type to check
  Future<RequestStatus> checkRequestStatus({
    required String currentUserId,
    required String targetUserId,
    RequestType? requestType,
  }) async {
    try {
      // Check cache first
      final cachedStates = await _cache.getCachedRequestStates(userId: currentUserId);
      if (cachedStates != null) {
        final status = _analyzeRequestStatus(cachedStates, targetUserId, requestType);
        if (status != RequestStatus.none) {
          return status;
        }
      }

      // Fallback to server check
      final userModel = await _loadFromServer(currentUserId);
      if (userModel != null) {
        await _cache.cacheRequestStates(userModel: userModel);
        return _analyzeUserModelRequestStatus(userModel, targetUserId, requestType);
      }

      return RequestStatus.none;
    } catch (e) {
      LoggingService.error('Failed to check request status: $e');
      return RequestStatus.none;
    }
  }

  /// Updates request state when a request is sent.
  /// 
  /// [currentUserId] - The user sending the request
  /// [targetUserId] - The user receiving the request
  /// [requestType] - The type of request being sent
  Future<void> onRequestSent({
    required String currentUserId,
    required String targetUserId,
    required RequestType requestType,
  }) async {
    try {
      await _cache.updateRequestState(
        userId: currentUserId,
        targetUserId: targetUserId,
        requestType: requestType,
        action: RequestAction.add,
        sent: true,
      );

      _emitStateUpdate(RequestStateUpdate(
        userId: currentUserId,
        updateType: RequestUpdateType.requestSent,
        requestType: requestType,
        targetUserId: targetUserId,
      ));

      LoggingService.info('Request sent: $requestType from $currentUserId to $targetUserId');
    } catch (e) {
      LoggingService.error('Failed to update request sent state: $e');
    }
  }

  /// Updates request state when a request is received.
  /// 
  /// [currentUserId] - The user receiving the request
  /// [senderUserId] - The user who sent the request
  /// [requestType] - The type of request received
  Future<void> onRequestReceived({
    required String currentUserId,
    required String senderUserId,
    required RequestType requestType,
  }) async {
    try {
      await _cache.updateRequestState(
        userId: currentUserId,
        targetUserId: senderUserId,
        requestType: requestType,
        action: RequestAction.add,
        sent: false,
      );

      _emitStateUpdate(RequestStateUpdate(
        userId: currentUserId,
        updateType: RequestUpdateType.requestReceived,
        requestType: requestType,
        targetUserId: senderUserId,
      ));

      LoggingService.info('Request received: $requestType from $senderUserId to $currentUserId');
    } catch (e) {
      LoggingService.error('Failed to update request received state: $e');
    }
  }

  /// Updates request state when a request is accepted.
  /// 
  /// [currentUserId] - The user accepting the request
  /// [otherUserId] - The other user involved in the request
  /// [requestType] - The type of request being accepted
  Future<void> onRequestAccepted({
    required String currentUserId,
    required String otherUserId,
    required RequestType requestType,
  }) async {
    try {
      // Remove from both sent and received lists
      await _cache.updateRequestState(
        userId: currentUserId,
        targetUserId: otherUserId,
        requestType: requestType,
        action: RequestAction.remove,
        sent: true,
      );

      await _cache.updateRequestState(
        userId: currentUserId,
        targetUserId: otherUserId,
        requestType: requestType,
        action: RequestAction.remove,
        sent: false,
      );

      _emitStateUpdate(RequestStateUpdate(
        userId: currentUserId,
        updateType: RequestUpdateType.requestAccepted,
        requestType: requestType,
        targetUserId: otherUserId,
      ));

      LoggingService.info('Request accepted: $requestType between $currentUserId and $otherUserId');
    } catch (e) {
      LoggingService.error('Failed to update request accepted state: $e');
    }
  }

  /// Updates request state when a request is declined.
  /// 
  /// [currentUserId] - The user declining the request
  /// [otherUserId] - The other user involved in the request
  /// [requestType] - The type of request being declined
  Future<void> onRequestDeclined({
    required String currentUserId,
    required String otherUserId,
    required RequestType requestType,
  }) async {
    try {
      // Remove from both sent and received lists
      await _cache.updateRequestState(
        userId: currentUserId,
        targetUserId: otherUserId,
        requestType: requestType,
        action: RequestAction.remove,
        sent: true,
      );

      await _cache.updateRequestState(
        userId: currentUserId,
        targetUserId: otherUserId,
        requestType: requestType,
        action: RequestAction.remove,
        sent: false,
      );

      _emitStateUpdate(RequestStateUpdate(
        userId: currentUserId,
        updateType: RequestUpdateType.requestDeclined,
        requestType: requestType,
        targetUserId: otherUserId,
      ));

      LoggingService.info('Request declined: $requestType between $currentUserId and $otherUserId');
    } catch (e) {
      LoggingService.error('Failed to update request declined state: $e');
    }
  }

  /// Refreshes request states from the server.
  /// 
  /// [userId] - The user ID to refresh for
  Future<UserModel?> refreshFromServer(String userId) async {
    return await initializeForUser(userId: userId, forceRefresh: true);
  }

  /// Clears all cached request states.
  Future<void> clearCache() async {
    await _cache.clearCache();
    LoggingService.info('Request state manager cache cleared');
  }

  /// Disposes of the request state manager.
  void dispose() {
    _requestStateController.close();
  }

  // Private helper methods

  Future<UserModel?> _loadFromCache(String userId) async {
    final cachedStates = await _cache.getCachedRequestStates(userId: userId);
    if (cachedStates == null) return null;

    // Convert cached data back to UserModel
    // This is a simplified conversion - in practice, you'd need the full user data
    return null; // Would need to implement full UserModel reconstruction
  }

  Future<UserModel?> _loadFromServer(String userId) async {
    try {
      final response = await _apiService.get('/social/enhanced-profile/$userId');
      return UserModel.fromJson(response);
    } catch (e) {
      LoggingService.error('Failed to load enhanced profile from server: $e');
      return null;
    }
  }

  RequestStatus _analyzeRequestStatus(
    Map<String, dynamic> cachedStates,
    String targetUserId,
    RequestType? requestType,
  ) {
    // Analyze cached states to determine request status
    // Implementation would check all relevant fields
    return RequestStatus.none;
  }

  RequestStatus _analyzeUserModelRequestStatus(
    UserModel userModel,
    String targetUserId,
    RequestType? requestType,
  ) {
    // Analyze UserModel to determine request status
    // Implementation would check all relevant fields
    return RequestStatus.none;
  }

  void _emitStateUpdate(RequestStateUpdate update) {
    if (!_requestStateController.isClosed) {
      _requestStateController.add(update);
    }
  }
}

/// Represents a request state update.
class RequestStateUpdate {
  const RequestStateUpdate({
    required this.userId,
    required this.updateType,
    this.requestType,
    this.targetUserId,
    this.userModel,
  });

  final String userId;
  final RequestUpdateType updateType;
  final RequestType? requestType;
  final String? targetUserId;
  final UserModel? userModel;
}

/// Types of request updates.
enum RequestUpdateType {
  initialization,
  requestSent,
  requestReceived,
  requestAccepted,
  requestDeclined,
  refresh,
}

/// Request status between two users.
enum RequestStatus {
  none,
  sentPending,
  receivedPending,
  accepted,
  declined,
}
