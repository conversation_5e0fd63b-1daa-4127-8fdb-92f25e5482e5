import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';

/// Service for caching and retrieving request states across app restarts.
/// 
/// This service provides comprehensive request state persistence for all types
/// of requests in the Hopen app: contact, bubble start, invite, join, propose, and kickout.
/// 
/// Features:
/// - Persistent storage using SharedPreferences
/// - Request state expiration handling
/// - Comprehensive request type support
/// - Error handling and logging
/// - Cache cleanup and maintenance
class RequestStateCache {
  static const String _requestStatesKey = 'hopen_request_states';
  static const String _lastUpdateKey = 'hopen_request_states_last_update';
  static const Duration _cacheExpiration = Duration(hours: 24);

  /// Caches the current request states from a UserModel.
  /// 
  /// This method extracts all request-related data from the enhanced profile
  /// and stores it locally for persistence across app restarts.
  /// 
  /// [userModel] - The user model containing current request states
  /// [timestamp] - Optional timestamp (defaults to current time)
  Future<void> cacheRequestStates({
    required UserModel userModel,
    DateTime? timestamp,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = timestamp ?? DateTime.now();

      final requestStates = {
        'user_id': userModel.id,
        'timestamp': now.toIso8601String(),
        
        // Contact requests
        'pending_sent_contact_request_ids': userModel.pendingSentContactRequestIds,
        'pending_received_contact_request_ids': userModel.pendingReceivedContactRequestIds,
        
        // Legacy bubble requests (for backward compatibility)
        'pending_sent_bubble_request_user_ids': userModel.pendingSentBubbleRequestUserIds,
        'pending_received_bubble_request_user_ids': userModel.pendingReceivedBubbleRequestUserIds,
        
        // Specific bubble request types
        'pending_sent_bubble_start_request_user_ids': userModel.pendingSentBubbleStartRequestUserIds,
        'pending_received_bubble_start_request_user_ids': userModel.pendingReceivedBubbleStartRequestUserIds,
        'pending_sent_bubble_invite_request_user_ids': userModel.pendingSentBubbleInviteRequestUserIds,
        'pending_received_bubble_invite_request_user_ids': userModel.pendingReceivedBubbleInviteRequestUserIds,
        'pending_sent_bubble_join_request_bubble_ids': userModel.pendingSentBubbleJoinRequestBubbleIds,
        'pending_received_bubble_join_request_user_ids': userModel.pendingReceivedBubbleJoinRequestUserIds,
        'pending_sent_bubble_propose_request_user_ids': userModel.pendingSentBubbleProposeRequestUserIds,
        'pending_received_bubble_propose_request_user_ids': userModel.pendingReceivedBubbleProposeRequestUserIds,
        'pending_sent_bubble_kickout_request_user_ids': userModel.pendingSentBubbleKickoutRequestUserIds,
        'pending_received_bubble_kickout_request_user_ids': userModel.pendingReceivedBubbleKickoutRequestUserIds,
        
        // Additional metadata
        'contact_ids': userModel.contactIds,
        'friend_ids': userModel.friendIds,
        'bubble_id': userModel.bubbleId,
      };

      await prefs.setString(_requestStatesKey, jsonEncode(requestStates));
      await prefs.setString(_lastUpdateKey, now.toIso8601String());

      LoggingService.info('Cached request states for user ${userModel.id}');
    } catch (e) {
      LoggingService.error('Failed to cache request states: $e');
      // Don't throw - this is a fallback mechanism
    }
  }

  /// Retrieves cached request states for a user.
  /// 
  /// Returns null if no cached states exist, cache is expired, or an error occurs.
  /// 
  /// [userId] - The user ID to retrieve states for
  /// [maxAge] - Maximum age of cached data (defaults to 24 hours)
  Future<Map<String, dynamic>?> getCachedRequestStates({
    required String userId,
    Duration? maxAge,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_requestStatesKey);
      final lastUpdateStr = prefs.getString(_lastUpdateKey);

      if (cachedData == null || lastUpdateStr == null) {
        return null;
      }

      final lastUpdate = DateTime.parse(lastUpdateStr);
      final age = DateTime.now().difference(lastUpdate);
      final maxCacheAge = maxAge ?? _cacheExpiration;

      if (age > maxCacheAge) {
        LoggingService.info('Request state cache expired (age: ${age.inHours}h)');
        await clearCache();
        return null;
      }

      final requestStates = jsonDecode(cachedData) as Map<String, dynamic>;
      final cachedUserId = requestStates['user_id'] as String?;

      if (cachedUserId != userId) {
        LoggingService.info('Request state cache is for different user');
        await clearCache();
        return null;
      }

      LoggingService.info('Retrieved cached request states for user $userId');
      return requestStates;
    } catch (e) {
      LoggingService.error('Failed to retrieve cached request states: $e');
      return null;
    }
  }

  /// Checks if there are pending requests of a specific type for a user.
  /// 
  /// This method checks both cached data and provides fallback logic.
  /// 
  /// [userId] - The user ID to check
  /// [targetUserId] - The target user ID for the request
  /// [requestType] - The type of request to check
  Future<bool> hasPendingRequest({
    required String userId,
    required String targetUserId,
    required RequestType requestType,
  }) async {
    try {
      final cachedStates = await getCachedRequestStates(userId: userId);
      if (cachedStates == null) {
        return false;
      }

      final fieldName = _getRequestFieldName(requestType, sent: true);
      final pendingRequests = _parseStringList(cachedStates[fieldName]);
      
      return pendingRequests.contains(targetUserId);
    } catch (e) {
      LoggingService.error('Failed to check pending request: $e');
      return false;
    }
  }

  /// Updates a specific request state in the cache.
  /// 
  /// This method allows for granular updates without requiring a full cache refresh.
  /// 
  /// [userId] - The user ID
  /// [targetUserId] - The target user ID
  /// [requestType] - The type of request
  /// [action] - The action taken (add, remove)
  /// [sent] - Whether this is a sent or received request
  Future<void> updateRequestState({
    required String userId,
    required String targetUserId,
    required RequestType requestType,
    required RequestAction action,
    required bool sent,
  }) async {
    try {
      final cachedStates = await getCachedRequestStates(userId: userId);
      if (cachedStates == null) {
        return;
      }

      final fieldName = _getRequestFieldName(requestType, sent: sent);
      final currentRequests = _parseStringList(cachedStates[fieldName]);

      switch (action) {
        case RequestAction.add:
          if (!currentRequests.contains(targetUserId)) {
            currentRequests.add(targetUserId);
          }
          break;
        case RequestAction.remove:
          currentRequests.remove(targetUserId);
          break;
      }

      cachedStates[fieldName] = currentRequests;
      cachedStates['timestamp'] = DateTime.now().toIso8601String();

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_requestStatesKey, jsonEncode(cachedStates));
      await prefs.setString(_lastUpdateKey, DateTime.now().toIso8601String());

      LoggingService.info('Updated request state: $requestType, $action, sent: $sent');
    } catch (e) {
      LoggingService.error('Failed to update request state: $e');
    }
  }

  /// Clears all cached request states.
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_requestStatesKey);
      await prefs.remove(_lastUpdateKey);
      LoggingService.info('Cleared request state cache');
    } catch (e) {
      LoggingService.error('Failed to clear request state cache: $e');
    }
  }

  /// Gets the appropriate field name for a request type.
  String _getRequestFieldName(RequestType requestType, {required bool sent}) {
    final prefix = sent ? 'pending_sent' : 'pending_received';
    
    switch (requestType) {
      case RequestType.contact:
        return '${prefix}_contact_request_ids';
      case RequestType.bubbleStart:
        return '${prefix}_bubble_start_request_user_ids';
      case RequestType.bubbleInvite:
        return '${prefix}_bubble_invite_request_user_ids';
      case RequestType.bubbleJoin:
        return sent 
          ? '${prefix}_bubble_join_request_bubble_ids'
          : '${prefix}_bubble_join_request_user_ids';
      case RequestType.bubblePropose:
        return '${prefix}_bubble_propose_request_user_ids';
      case RequestType.bubbleKickout:
        return '${prefix}_bubble_kickout_request_user_ids';
    }
  }

  /// Parses a string list from cached data.
  List<String> _parseStringList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((e) => e.toString()).toList();
    }
    return [];
  }
}

/// Enum for different request types.
enum RequestType {
  contact,
  bubbleStart,
  bubbleInvite,
  bubbleJoin,
  bubblePropose,
  bubbleKickout,
}

/// Enum for request actions.
enum RequestAction {
  add,
  remove,
}
