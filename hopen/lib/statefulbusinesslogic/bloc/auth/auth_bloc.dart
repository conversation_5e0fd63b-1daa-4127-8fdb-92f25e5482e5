import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// import '../../../repositories/usecase/login_usecase.dart';

// Placeholder for actual repository path post-refactor
import '../../../repositories/auth/auth_repository.dart';
// Current path for UserModel, same as in auth_event.dart
import '../../core/services/logging_service.dart';
// import '../../../statefulbusinesslogic/core/models/user_model.dart'
//     as core_models; // Keep this commented or remove if UserModel above is sufficient

// Current path for LoginUseCase
import '../../core/usecases/login_usecase.dart';
import '../../../repositories/auth/auth_repository.dart';

import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {

  AuthBloc({
    required LoginUseCase loginUseCase,
    required AuthRepository authRepository,
  }) : _loginUseCase = loginUseCase,
       _authRepository = authRepository,
       super(const AuthState.initial()) {
    on<CheckAuthStatusEvent>(_onCheckAuthStatus);
    on<LoginEvent>(_onLogin);
    on<LoginWithGoogleEvent>(_onLoginWithGoogle);
    on<LoginWithAppleEvent>(_onLoginWithApple);
    on<LogoutEvent>(_onLogout);
    on<UserLoggedInEvent>(_onUserLoggedIn);
    on<MarkOnboardingCompleteEvent>(_onMarkOnboardingComplete);
    on<UpdateUserInfoEvent>(_onUpdateUserInfo);
    on<SendPasswordResetEmailEvent>(_onSendPasswordResetEmail);
    on<SignInWithEmailAndPasswordEvent>(_onSignInWithEmailAndPassword);
    on<SignUpWithEmailAndPasswordEvent>(_onSignUpWithEmailAndPassword);
    on<SignOutEvent>(_onSignOut);
    on<AuthenticationInconsistencyDetected>(_onAuthenticationInconsistencyDetected);

    // TODO: Listen to auth state changes through repository
  }
  final LoginUseCase _loginUseCase;
  final AuthRepository _authRepository;

  Future<void> _onCheckAuthStatus(
    CheckAuthStatusEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));

    try {
      print('🔍 AuthBloc._onCheckAuthStatus: Using AuthRepository to check auth status');
      final result = await _authRepository.getCurrentUser();

      result.fold(
        onFailure: (failure) {
          print('🔍 AuthBloc._onCheckAuthStatus: User is not authenticated');
          emit(state.copyWith(status: AuthStatus.unauthenticated));
        },
        onSuccess: (user) {
          print('🔍 AuthBloc._onCheckAuthStatus: User is authenticated: ${user.email}');
          emit(state.copyWith(
            status: AuthStatus.authenticated,
            userId: user.id,
            email: user.email,
            firstName: user.firstName ?? '',
          ));
        },
      );
    } catch (e) {
      print('🔍 AuthBloc._onCheckAuthStatus: Error checking auth status: $e');
      emit(state.copyWith(status: AuthStatus.unauthenticated));
    }
  }

  Future<void> _onLogin(LoginEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.loading));

    try {
      print('🔍 AuthBloc._onLogin: Using AuthRepository for login');
      final result = await _authRepository.login(
        email: event.email,
        password: event.password,
      );

      result.fold(
        onFailure: (failure) {
          print('🔍 AuthBloc._onLogin: Login failed - ${failure.userMessage}');
          emit(state.copyWith(
            status: AuthStatus.error,
            errorMessage: failure.userMessage,
          ));
        },
        onSuccess: (user) {
          print('🔍 AuthBloc._onLogin: Login successful, user: ${user.email}');
          emit(state.copyWith(
            status: AuthStatus.authenticated,
            userId: user.id,
            email: user.email,
            firstName: user.firstName ?? '',
          ));
        },
      );
    } catch (e) {
      print('🔍 AuthBloc._onLogin: Login failed with error: $e');
      emit(state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'Login failed: ${e.toString()}',
      ));
    }
  }

  Future<void> _onLoginWithGoogle(
    LoginWithGoogleEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));
    final result = await _authRepository.loginWithGoogle();
    if (result.isSuccess) {
      emit(state.copyWith(
        status: AuthStatus.authenticated,
        userId: result.data.id,
        email: result.data.email,
        firstName: result.data.firstName,
      ));
    } else {
      emit(state.copyWith(
        status: AuthStatus.error,
        errorMessage: result.error.userMessage,
      ));
    }
  }

  Future<void> _onLoginWithApple(
    LoginWithAppleEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(status: AuthStatus.loading));
    final result = await _authRepository.loginWithApple();
    if (result.isSuccess) {
      emit(state.copyWith(
        status: AuthStatus.authenticated,
        userId: result.data.id,
        email: result.data.email,
        firstName: result.data.firstName,
      ));
    } else {
      emit(state.copyWith(
        status: AuthStatus.error,
        errorMessage: result.error.userMessage,
      ));
    }
  }

  Future<void> _onLogout(LogoutEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.loading));

    try {
      print('🔍 AuthBloc._onLogout: Using AuthRepository for logout');
      await _authRepository.logout();
      print('🔍 AuthBloc._onLogout: Logout successful');
    } catch (e) {
      print('🔍 AuthBloc._onLogout: Logout error: $e');
      // Continue with logout even if remote logout fails
    }

    // Always emit unauthenticated state regardless of remote logout result
    emit(const AuthState.unauthenticated());
  }

  Future<void> _onUserLoggedIn(
    UserLoggedInEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(state.copyWith(
      status: AuthStatus.authenticated,
      userId: event.user.id,
      email: event.user.email,
      firstName: event.user.firstName,
    ));
  }

  Future<void> _onMarkOnboardingComplete(
    MarkOnboardingCompleteEvent event,
    Emitter<AuthState> emit,
  ) async {
    if (state.status == AuthStatus.authenticated) {
      // Update local state immediately
      emit(state.copyWith(status: AuthStatus.authenticated));
      
      // Update backend asynchronously
      try {
        final result = await _authRepository.updateOnboardingStatus(true);
        if (result.isSuccess) {
          LoggingService.error('Onboarding status updated successfully in backend');
        } else {
          // Log error but don't revert state since user has completed onboarding
          LoggingService.error('Failed to update onboarding status in backend: ${result.error.userMessage}');
        }
      } on Exception catch (e) {
        // Log error but don't revert state
        LoggingService.error('Error updating onboarding status: $e');
      }
    }
  }

  Future<void> _onUpdateUserInfo(
    UpdateUserInfoEvent event,
    Emitter<AuthState> emit,
  ) async {
    if (state.status == AuthStatus.authenticated) {
      emit(state.copyWith(
        firstName: event.firstName,
      ));
    }
  }

  Future<void> _onSendPasswordResetEmail(SendPasswordResetEmailEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.loading));
    try {
      // Use the Ory auth service to initiate password reset
      // For now, simulate password reset since we don't have the specific method
      // In a real implementation, you'd add initiatePasswordReset to AuthRepository

      // Simulate successful password reset
      emit(state.copyWith(status: AuthStatus.passwordResetSent));
    } on Exception catch (e) {
      emit(state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'Password reset failed: ${e.toString()}',
      ));
    }
  }

  Future<void> _onSignInWithEmailAndPassword(SignInWithEmailAndPasswordEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.loading));
    try {
      // Use the auth repository to sign in
      final result = await _authRepository.login(
        email: event.email,
        password: event.password,
      );

      if (result.isSuccess) {
        final user = result.data;
        emit(state.copyWith(
          status: AuthStatus.authenticated,
          userId: user.id,
          email: user.email,
          firstName: user.firstName,
        ));
      } else {
        emit(state.copyWith(
          status: AuthStatus.error,
          errorMessage: result.error.userMessage,
        ));
      }
    } on Exception catch (e) {
      emit(state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'Sign in failed: ${e.toString()}',
      ));
    }
  }

  Future<void> _onSignUpWithEmailAndPassword(SignUpWithEmailAndPasswordEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.loading));
    try {
      // Use the auth repository to sign up
      final result = await _authRepository.signUp(
        email: event.email,
        password: event.password,
        username: event.email.split('@')[0], // Use email prefix as username
        firstName: 'User', // Placeholder first name
        lastName: 'Name', // Placeholder last name
      );

      if (result.isSuccess) {
        final user = result.data;
        emit(state.copyWith(
          status: AuthStatus.authenticated,
          userId: user.id,
          email: user.email,
          firstName: user.firstName,
        ));
      } else {
        emit(state.copyWith(
          status: AuthStatus.error,
          errorMessage: result.error.userMessage,
        ));
      }
    } on Exception catch (e) {
      emit(state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'Sign up failed: ${e.toString()}',
      ));
    }
  }

  Future<void> _onSignOut(SignOutEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.loading));
    try {
      await _authRepository.logout();
      emit(const AuthState.unauthenticated());
    } on Exception catch (e) {
      emit(state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'Sign out failed: ${e.toString()}',
      ));
    }
  }

  Future<void> _onAuthenticationInconsistencyDetected(
    AuthenticationInconsistencyDetected event,
    Emitter<AuthState> emit,
  ) async {
    LoggingService.error(
      'Authentication inconsistency detected: ${event.details}',
    );
    // Force sign-out to resolve inconsistency
    await _onSignOut(const SignOutEvent(), emit);
  }

  /// Trigger contact request checking when user becomes authenticated
  void _triggerContactRequestCheck() {
    // Use a short delay to ensure the UI is ready
    Future.delayed(const Duration(milliseconds: 500), () {
      // We need a BuildContext to show the dialog, but we don't have one in the BLoC
      // Instead, we'll emit an event that the UI can listen to
      LoggingService.info('AuthBloc: User authenticated, contact request checking should be triggered by UI');
    });
  }

  @override
  Future<void> close() async {
    // No subscriptions to cancel anymore
    return super.close();
  }
}
