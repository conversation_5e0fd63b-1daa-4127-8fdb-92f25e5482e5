import 'package:equatable/equatable.dart';
// Assuming UserModel will eventually be at a path like this after refactor:
import '../../core/models/user_model.dart'; // Placeholder for actual model path

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class CheckAuthStatusEvent extends AuthEvent {
  const CheckAuthStatusEvent();
}

class LoginEvent extends AuthEvent {
  const LoginEvent({required this.email, required this.password});
  final String email;
  final String password;

  @override
  List<Object> get props => [email, password];
}

class LoginWithGoogleEvent extends AuthEvent {
  const LoginWithGoogleEvent();
}

class LoginWithAppleEvent extends AuthEvent {
  const LoginWithAppleEvent();
}

class LogoutEvent extends AuthEvent {
  const LogoutEvent();
}

class UserLoggedInEvent extends AuthEvent {
  const UserLoggedInEvent(this.user);
  final UserModel user;

  @override
  List<Object?> get props => [user];
}

class MarkOnboardingCompleteEvent extends AuthEvent {
  const MarkOnboardingCompleteEvent();
}

class UpdateUserInfoEvent extends AuthEvent {
  const UpdateUserInfoEvent({
    required this.firstName,
    this.lastName,
    this.username,
  });
  final String firstName;
  final String? lastName;
  final String? username;

  @override
  List<Object?> get props => [firstName, lastName, username];
}

class SendPasswordResetEmailEvent extends AuthEvent {
  const SendPasswordResetEmailEvent({required this.email});
  final String email;

  @override
  List<Object> get props => [email];
}

class SignInWithEmailAndPasswordEvent extends AuthEvent {
  const SignInWithEmailAndPasswordEvent({
    required this.email,
    required this.password,
  });
  final String email;
  final String password;

  @override
  List<Object> get props => [email, password];
}

class SignUpWithEmailAndPasswordEvent extends AuthEvent {
  const SignUpWithEmailAndPasswordEvent({
    required this.email,
    required this.password,
  });
  final String email;
  final String password;

  @override
  List<Object> get props => [email, password];
}

class SignOutEvent extends AuthEvent {
  const SignOutEvent();
}

class AuthenticationInconsistencyDetected extends AuthEvent {
  const AuthenticationInconsistencyDetected({required this.details});
  final String details;

  @override
  List<Object?> get props => [details];
}
