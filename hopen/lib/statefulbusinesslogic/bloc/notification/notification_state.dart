import 'package:equatable/equatable.dart';
import '../../core/models/notification_model.dart';

/// Base class for all notification states
abstract class NotificationState extends Equatable {
  const NotificationState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class NotificationInitial extends NotificationState {
  const NotificationInitial();
}

/// Loading state when fetching notifications
class NotificationLoading extends NotificationState {
  const NotificationLoading();
}

/// State when notifications have been successfully loaded
class NotificationsLoaded extends NotificationState {
  const NotificationsLoaded({
    required this.notifications,
    required this.unreadCount,
  });
  final List<Notification> notifications;
  final int unreadCount;

  @override
  List<Object?> get props => [notifications, unreadCount];

  /// Create a copy of this state with given fields replaced
  NotificationsLoaded copyWith({
    List<Notification>? notifications,
    int? unreadCount,
  }) => NotificationsLoaded(
    notifications: notifications ?? this.notifications,
    unreadCount: unreadCount ?? this.unreadCount,
  );
}

/// Error state when notifications could not be loaded
class NotificationError extends NotificationState {
  const NotificationError(this.message);
  final String message;

  @override
  List<Object?> get props => [message];
}

/// State when a bubble expiry notification is received
class BubbleExpiryNotificationReceived extends NotificationState {
  const BubbleExpiryNotificationReceived({
    required this.notification,
    required this.bubbleId,
    required this.daysLeft,
  });

  final Notification notification;
  final String bubbleId;
  final int daysLeft;

  @override
  List<Object?> get props => [notification, bubbleId, daysLeft];
}

/// State when a contact request notification is received
class ContactRequestNotificationReceived extends NotificationState {
  const ContactRequestNotificationReceived({
    required this.requestId,
    required this.requesterId,
    required this.requesterName,
    required this.requesterUsername,
    required this.requesterProfilePicUrl,
    required this.requestTimestamp,
  });

  final String requestId;
  final String requesterId;
  final String requesterName;
  final String? requesterUsername;
  final String? requesterProfilePicUrl;
  final DateTime requestTimestamp;

  @override
  List<Object?> get props => [
    requestId,
    requesterId,
    requesterName,
    requesterUsername,
    requesterProfilePicUrl,
    requestTimestamp,
  ];
}

/// State when a bubble start request notification is received
class BubbleStartRequestNotificationReceived extends NotificationState {
  const BubbleStartRequestNotificationReceived({
    required this.requestId,
    required this.requesterId,
    required this.requesterName,
    required this.requesterUsername,
    required this.requesterProfilePicUrl,
    required this.requestTimestamp,
  });

  final String requestId;
  final String requesterId;
  final String requesterName;
  final String? requesterUsername;
  final String? requesterProfilePicUrl;
  final DateTime requestTimestamp;

  @override
  List<Object?> get props => [
    requestId,
    requesterId,
    requesterName,
    requesterUsername,
    requesterProfilePicUrl,
    requestTimestamp,
  ];
}

/// State when a bubble invite request notification is received
class BubbleInviteRequestNotificationReceived extends NotificationState {
  const BubbleInviteRequestNotificationReceived({
    required this.requestId,
    required this.inviterId,
    required this.inviterName,
    required this.inviterUsername,
    required this.inviterProfilePicUrl,
    required this.inviteTimestamp,
    required this.bubbleId,
    required this.bubbleName,
  });

  final String requestId;
  final String inviterId;
  final String inviterName;
  final String? inviterUsername;
  final String? inviterProfilePicUrl;
  final DateTime inviteTimestamp;
  final String bubbleId;
  final String bubbleName;

  @override
  List<Object?> get props => [
    requestId,
    inviterId,
    inviterName,
    inviterUsername,
    inviterProfilePicUrl,
    inviteTimestamp,
    bubbleId,
    bubbleName,
  ];
}

/// State when a bubble join request notification is received
class BubbleJoinRequestNotificationReceived extends NotificationState {
  const BubbleJoinRequestNotificationReceived({
    required this.requestId,
    required this.requesterId,
    required this.requesterName,
    required this.requesterUsername,
    required this.requesterProfilePicUrl,
    required this.requestTimestamp,
    required this.bubbleId,
    required this.bubbleName,
  });

  final String requestId;
  final String requesterId;
  final String requesterName;
  final String? requesterUsername;
  final String? requesterProfilePicUrl;
  final DateTime requestTimestamp;
  final String bubbleId;
  final String bubbleName;

  @override
  List<Object?> get props => [
    requestId,
    requesterId,
    requesterName,
    requesterUsername,
    requesterProfilePicUrl,
    requestTimestamp,
    bubbleId,
    bubbleName,
  ];
}

/// State when a bubble kickout request notification is received
class BubbleKickoutRequestNotificationReceived extends NotificationState {
  const BubbleKickoutRequestNotificationReceived({
    required this.requestId,
    required this.targetMemberId,
    required this.targetMemberName,
    required this.requesterId,
    required this.requesterName,
    required this.targetMemberUsername,
    required this.targetMemberProfilePicUrl,
    required this.requestTimestamp,
    required this.bubbleId,
    required this.bubbleName,
  });

  final String requestId;
  final String targetMemberId;
  final String targetMemberName;
  final String requesterId;
  final String requesterName;
  final String? targetMemberUsername;
  final String? targetMemberProfilePicUrl;
  final DateTime requestTimestamp;
  final String bubbleId;
  final String bubbleName;

  @override
  List<Object?> get props => [
    requestId,
    targetMemberId,
    targetMemberName,
    requesterId,
    requesterName,
    targetMemberUsername,
    targetMemberProfilePicUrl,
    requestTimestamp,
    bubbleId,
    bubbleName,
  ];
}

/// State when a bubble propose request notification is received
class BubbleProposeRequestNotificationReceived extends NotificationState {
  const BubbleProposeRequestNotificationReceived({
    required this.proposedMemberId,
    required this.proposedMemberName,
    required this.proposerId,
    required this.proposerName,
    required this.proposedMemberUsername,
    required this.proposedMemberProfilePicUrl,
    required this.proposeTimestamp,
    required this.bubbleId,
    required this.bubbleName,
  });

  final String proposedMemberId;
  final String proposedMemberName;
  final String proposerId;
  final String proposerName;
  final String? proposedMemberUsername;
  final String? proposedMemberProfilePicUrl;
  final DateTime proposeTimestamp;
  final String bubbleId;
  final String bubbleName;

  @override
  List<Object?> get props => [
    proposedMemberId,
    proposedMemberName,
    proposerId,
    proposerName,
    proposedMemberUsername,
    proposedMemberProfilePicUrl,
    proposeTimestamp,
    bubbleId,
    bubbleName,
  ];
}

/// State when a friends choice request notification is received (bubble expired)
class FriendsChoiceRequestNotificationReceived extends NotificationState {
  const FriendsChoiceRequestNotificationReceived({
    required this.bubbleId,
    required this.bubbleName,
    required this.formerMembersData,
  });

  final String bubbleId;
  final String bubbleName;
  final List<Map<String, dynamic>> formerMembersData;

  @override
  List<Object?> get props => [
    bubbleId,
    bubbleName,
    formerMembersData,
  ];
}
