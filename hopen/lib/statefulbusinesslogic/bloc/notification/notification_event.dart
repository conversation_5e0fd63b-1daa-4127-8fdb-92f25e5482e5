import 'package:equatable/equatable.dart';
import '../../core/models/notification_model.dart';

/// Base class for all notification events
abstract class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object> get props => [];
}

/// Event to fetch all notifications
class FetchNotifications extends NotificationEvent {
  const FetchNotifications();
}

/// Event to fetch notification count
class FetchUnreadCount extends NotificationEvent {
  const FetchUnreadCount();
}

/// Event when a new notification is received
class NotificationReceived extends NotificationEvent {
  const NotificationReceived(this.notification);
  final Notification notification;

  @override
  List<Object> get props => [notification];
}

/// Event to mark a notification as read
class MarkNotificationAsRead extends NotificationEvent {
  const MarkNotificationAsRead(this.notificationId);
  final String notificationId;

  @override
  List<Object> get props => [notificationId];
}

/// Event to mark a notification as unread
class MarkNotificationAsUnread extends NotificationEvent {
  const MarkNotificationAsUnread(this.notificationId);
  final String notificationId;

  @override
  List<Object> get props => [notificationId];
}

/// Event to mark all notifications as read
class MarkAllNotificationsAsRead extends NotificationEvent {
  const MarkAllNotificationsAsRead();
}

/// Event to delete a notification
class DeleteNotification extends NotificationEvent {
  const DeleteNotification(this.notificationId);
  final String notificationId;

  @override
  List<Object> get props => [notificationId];
}

/// Event to delete all notifications
class DeleteAllNotifications extends NotificationEvent {
  const DeleteAllNotifications();
}

/// Event to add a new notification
class AddNotificationEvent extends NotificationEvent {
  const AddNotificationEvent({required this.notification});
  final Notification notification;

  @override
  List<Object> get props => [notification];
}

/// Legacy alias for AddNotificationEvent
class AddNotification extends AddNotificationEvent {
  const AddNotification({required super.notification});
}

/// Event when a contact request notification is received
class ContactRequestNotificationReceivedEvent extends NotificationEvent {
  const ContactRequestNotificationReceivedEvent({
    required this.requestId,
    required this.requesterId,
    required this.requesterName,
    required this.requesterUsername,
    required this.requesterProfilePicUrl,
    required this.requestTimestamp,
  });

  final String requestId;
  final String requesterId;
  final String requesterName;
  final String? requesterUsername;
  final String? requesterProfilePicUrl;
  final DateTime requestTimestamp;

  @override
  List<Object> get props => [
    requestId,
    requesterId,
    requesterName,
    requesterUsername ?? '',
    requesterProfilePicUrl ?? '',
    requestTimestamp,
  ];
}

/// Event when a bubble start request notification is received
class BubbleStartRequestNotificationReceivedEvent extends NotificationEvent {
  const BubbleStartRequestNotificationReceivedEvent({
    required this.requestId,
    required this.requesterId,
    required this.requesterName,
    required this.requesterUsername,
    required this.requesterProfilePicUrl,
    required this.requestTimestamp,
  });

  final String requestId;
  final String requesterId;
  final String requesterName;
  final String? requesterUsername;
  final String? requesterProfilePicUrl;
  final DateTime requestTimestamp;

  @override
  List<Object> get props => [
    requestId,
    requesterId,
    requesterName,
    requesterUsername ?? '',
    requesterProfilePicUrl ?? '',
    requestTimestamp,
  ];
}

/// Event when a bubble invite request notification is received
class BubbleInviteRequestNotificationReceivedEvent extends NotificationEvent {
  const BubbleInviteRequestNotificationReceivedEvent({
    required this.requestId,
    required this.inviterId,
    required this.inviterName,
    required this.bubbleId,
    required this.bubbleName,
    required this.requestTimestamp,
    this.inviterUsername,
    this.inviterProfilePicUrl,
  });

  final String requestId;
  final String inviterId;
  final String inviterName;
  final String bubbleId;
  final String bubbleName;
  final DateTime requestTimestamp;
  final String? inviterUsername;
  final String? inviterProfilePicUrl;

  @override
  List<Object> get props => [
    requestId,
    inviterId,
    inviterName,
    bubbleId,
    bubbleName,
    requestTimestamp,
    inviterUsername ?? '',
    inviterProfilePicUrl ?? '',
  ];
}

/// Event when a bubble join request notification is received
class BubbleJoinRequestNotificationReceivedEvent extends NotificationEvent {
  const BubbleJoinRequestNotificationReceivedEvent({
    required this.requestId,
    required this.requesterId,
    required this.requesterName,
    required this.bubbleId,
    required this.bubbleName,
    required this.requestTimestamp,
    this.requesterUsername,
    this.requesterProfilePicUrl,
  });

  final String requestId;
  final String requesterId;
  final String requesterName;
  final String bubbleId;
  final String bubbleName;
  final DateTime requestTimestamp;
  final String? requesterUsername;
  final String? requesterProfilePicUrl;

  @override
  List<Object> get props => [
    requestId,
    requesterId,
    requesterName,
    bubbleId,
    bubbleName,
    requestTimestamp,
    requesterUsername ?? '',
    requesterProfilePicUrl ?? '',
  ];
}

/// Event when a bubble kickout request notification is received
class BubbleKickoutRequestNotificationReceivedEvent extends NotificationEvent {
  const BubbleKickoutRequestNotificationReceivedEvent({
    required this.requestId,
    required this.requesterId,
    required this.requesterName,
    required this.targetMemberId,
    required this.targetMemberName,
    required this.bubbleId,
    required this.bubbleName,
    required this.requestTimestamp,
    this.requesterUsername,
    this.requesterProfilePicUrl,
    this.targetMemberUsername,
    this.targetMemberProfilePicUrl,
  });

  final String requestId;
  final String requesterId;
  final String requesterName;
  final String targetMemberId;
  final String targetMemberName;
  final String bubbleId;
  final String bubbleName;
  final DateTime requestTimestamp;
  final String? requesterUsername;
  final String? requesterProfilePicUrl;
  final String? targetMemberUsername;
  final String? targetMemberProfilePicUrl;

  @override
  List<Object> get props => [
    requestId,
    requesterId,
    requesterName,
    targetMemberId,
    targetMemberName,
    bubbleId,
    bubbleName,
    requestTimestamp,
    requesterUsername ?? '',
    requesterProfilePicUrl ?? '',
    targetMemberUsername ?? '',
    targetMemberProfilePicUrl ?? '',
  ];
}

/// Event when a friend request notification is received (auto-generated from bubble expiry)
class FriendRequestNotificationReceivedEvent extends NotificationEvent {
  const FriendRequestNotificationReceivedEvent({
    required this.requestId,
    required this.requesterId,
    required this.requesterName,
    required this.sourceBubbleId,
    required this.requestTimestamp,
    this.requesterUsername,
    this.requesterProfilePicUrl,
    this.bubbleName,
  });

  final String requestId;
  final String requesterId;
  final String requesterName;
  final String sourceBubbleId;
  final DateTime requestTimestamp;
  final String? requesterUsername;
  final String? requesterProfilePicUrl;
  final String? bubbleName;

  @override
  List<Object> get props => [
    requestId,
    requesterId,
    requesterName,
    sourceBubbleId,
    requestTimestamp,
    requesterUsername ?? '',
    requesterProfilePicUrl ?? '',
    bubbleName ?? '',
  ];
}
