import 'package:equatable/equatable.dart';

abstract class FriendRequestEvent extends Equatable {
  const FriendRequestEvent();

  @override
  List<Object?> get props => [];
}

class LoadFriendRequestEvent extends FriendRequestEvent {
  const LoadFriendRequestEvent({
    required this.requestId,
    required this.requesterId,
    required this.requesterName,
    required this.sourceBubbleId,
    this.requesterUsername,
    this.requesterProfilePicUrl,
    this.bubbleName,
  });
  
  final String requestId;
  final String requesterId;
  final String requesterName;
  final String sourceBubbleId;
  final String? requesterUsername;
  final String? requesterProfilePicUrl;
  final String? bubbleName;

  @override
  List<Object?> get props => [
    requestId,
    requesterId,
    requesterName,
    sourceBubbleId,
    requesterUsername,
    requesterProfilePicUrl,
    bubbleName,
  ];
}

class AcceptFriendRequestEvent extends FriendRequestEvent {
  const AcceptFriendRequestEvent({required this.requestId});
  final String requestId;

  @override
  List<Object> get props => [requestId];
}

class DeclineFriendRequestEvent extends FriendRequestEvent {
  const DeclineFriendRequestEvent({required this.requestId});
  final String requestId;

  @override
  List<Object> get props => [requestId];
}

class LoadPendingFriendRequestsEvent extends FriendRequestEvent {
  const LoadPendingFriendRequestsEvent();
}

class ResetFriendRequestEvent extends FriendRequestEvent {
  const ResetFriendRequestEvent();
}
