import 'package:equatable/equatable.dart';
import '../../core/models/friend_request.dart';

enum FriendRequestStatus {
  initial,
  loading,
  loaded,
  accepting,
  accepted,
  declining,
  declined,
  error,
}

class FriendRequestState extends Equatable {
  const FriendRequestState({
    this.status = FriendRequestStatus.initial,
    this.errorMessage,
    this.currentRequest,
    this.pendingRequests = const [],
    this.requestId = '',
    this.requesterId = '',
    this.requesterName = '',
    this.sourceBubbleId = '',
    this.requesterUsername,
    this.requesterProfilePicUrl,
    this.bubbleName,
  });

  final FriendRequestStatus status;
  final String? errorMessage;
  final FriendRequest? currentRequest;
  final List<FriendRequest> pendingRequests;
  
  // Individual request details for dialog display
  final String requestId;
  final String requesterId;
  final String requesterName;
  final String sourceBubbleId;
  final String? requesterUsername;
  final String? requesterProfilePicUrl;
  final String? bubbleName;

  FriendRequestState copyWith({
    FriendRequestStatus? status,
    String? errorMessage,
    FriendRequest? currentRequest,
    List<FriendRequest>? pendingRequests,
    String? requestId,
    String? requesterId,
    String? requesterName,
    String? sourceBubbleId,
    String? requesterUsername,
    String? requesterProfilePicUrl,
    String? bubbleName,
  }) => FriendRequestState(
    status: status ?? this.status,
    errorMessage: errorMessage,
    currentRequest: currentRequest ?? this.currentRequest,
    pendingRequests: pendingRequests ?? this.pendingRequests,
    requestId: requestId ?? this.requestId,
    requesterId: requesterId ?? this.requesterId,
    requesterName: requesterName ?? this.requesterName,
    sourceBubbleId: sourceBubbleId ?? this.sourceBubbleId,
    requesterUsername: requesterUsername ?? this.requesterUsername,
    requesterProfilePicUrl: requesterProfilePicUrl ?? this.requesterProfilePicUrl,
    bubbleName: bubbleName ?? this.bubbleName,
  );

  @override
  List<Object?> get props => [
    status,
    errorMessage,
    currentRequest,
    pendingRequests,
    requestId,
    requesterId,
    requesterName,
    sourceBubbleId,
    requesterUsername,
    requesterProfilePicUrl,
    bubbleName,
  ];
}
