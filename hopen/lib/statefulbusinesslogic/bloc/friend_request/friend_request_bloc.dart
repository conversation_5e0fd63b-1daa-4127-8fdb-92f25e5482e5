import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../repositories/friendship/friend_request_repository.dart';
import 'friend_request_event.dart';
import 'friend_request_state.dart';

class FriendRequestBloc extends Bloc<FriendRequestEvent, FriendRequestState> {
  FriendRequestBloc({
    required FriendRequestRepository repository,
  })  : _repository = repository,
        super(const FriendRequestState()) {
    on<LoadFriendRequestEvent>(_onLoadFriendRequest);
    on<AcceptFriendRequestEvent>(_onAcceptFriendRequest);
    on<DeclineFriendRequestEvent>(_onDeclineFriendRequest);
    on<LoadPendingFriendRequestsEvent>(_onLoadPendingFriendRequests);
    on<ResetFriendRequestEvent>(_onResetFriendRequest);
  }

  final FriendRequestRepository _repository;

  void _onLoadFriendRequest(
    LoadFriendRequestEvent event,
    Emitter<FriendRequestState> emit,
  ) {
    emit(state.copyWith(
      status: FriendRequestStatus.loaded,
      requestId: event.requestId,
      requesterId: event.requesterId,
      requesterName: event.requesterName,
      sourceBubbleId: event.sourceBubbleId,
      requesterUsername: event.requesterUsername,
      requesterProfilePicUrl: event.requesterProfilePicUrl,
      bubbleName: event.bubbleName,
    ));
  }

  Future<void> _onAcceptFriendRequest(
    AcceptFriendRequestEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    try {
      emit(state.copyWith(status: FriendRequestStatus.accepting));

      final result = await _repository.acceptFriendRequest(event.requestId);
      
      if (result.isSuccess) {
        emit(state.copyWith(status: FriendRequestStatus.accepted));
      } else {
        emit(state.copyWith(
          status: FriendRequestStatus.error,
          errorMessage: result.error?.userMessage ?? 'Failed to accept friend request',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        status: FriendRequestStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onDeclineFriendRequest(
    DeclineFriendRequestEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    try {
      emit(state.copyWith(status: FriendRequestStatus.declining));

      final result = await _repository.declineFriendRequest(event.requestId);
      
      if (result.isSuccess) {
        emit(state.copyWith(status: FriendRequestStatus.declined));
      } else {
        emit(state.copyWith(
          status: FriendRequestStatus.error,
          errorMessage: result.error?.userMessage ?? 'Failed to decline friend request',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        status: FriendRequestStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onLoadPendingFriendRequests(
    LoadPendingFriendRequestsEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    try {
      emit(state.copyWith(status: FriendRequestStatus.loading));

      final result = await _repository.getPendingFriendRequests();
      
      if (result.isSuccess) {
        emit(state.copyWith(
          status: FriendRequestStatus.loaded,
          pendingRequests: result.data ?? [],
        ));
      } else {
        emit(state.copyWith(
          status: FriendRequestStatus.error,
          errorMessage: result.error?.userMessage ?? 'Failed to load pending friend requests',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        status: FriendRequestStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  void _onResetFriendRequest(
    ResetFriendRequestEvent event,
    Emitter<FriendRequestState> emit,
  ) {
    emit(const FriendRequestState());
  }
}
