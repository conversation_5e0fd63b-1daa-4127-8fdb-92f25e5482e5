import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../repositories/bubble/bubble_repository.dart';
import '../../../repositories/contact/contact_request_repository.dart';
import '../../../repositories/contacts/contacts_repository.dart';
import '../../../repositories/user/user_repository.dart';
import '../../core/models/relationship_type.dart';
import '../../core/models/user_model.dart';
import '../../core/models/value_objects.dart';
import '../auth/auth_bloc.dart';
import '../../core/models/bubble_member.dart';
import '../../../di/injection_container_refactored.dart' as di;
import '../../../provider/services/request_state_manager.dart';
import '../../../provider/local_storage/request_state_cache.dart';
import '../../../provider/services/api/http_api_service.dart';
import 'unified_profile_event.dart';
import 'unified_profile_state.dart';

// BLoC
class UnifiedProfileBloc extends Bloc<UnifiedProfileEvent, UnifiedProfileState> {
  UnifiedProfileBloc({
    required UserRepository userRepository,
    required BubbleRepository bubbleRepository,
    required ContactRequestRepository contactRequestRepository,
    required AuthBloc authBloc,
    RequestStateManager? requestStateManager,
  })  : _userRepository = userRepository,
        _bubbleRepository = bubbleRepository,
        _contactRequestRepository = contactRequestRepository,
        _authBloc = authBloc,
        _requestStateManager = requestStateManager ?? RequestStateManager(
          apiService: di.sl<HttpApiService>(),
        ),
        super(UnifiedProfileInitial()) {
    on<LoadUnifiedProfile>(_onLoadUnifiedProfile);
    on<SendContactRequest>(_onSendContactRequest);
    on<SendBubbleRequestEvent>(_onSendBubbleRequest);
    on<ContactRequestAcceptedEvent>(_onContactRequestAccepted);
    on<ContactRequestDeclinedEvent>(_onContactRequestDeclined);
    on<BubbleRequestAcceptedEvent>(_onBubbleRequestAccepted);
    on<BubbleRequestDeclinedEvent>(_onBubbleRequestDeclined);
    on<UnblockUser>(_onUnblockUser);
    on<UnfriendUserEvent>(_onUnfriendUser);
    on<MuteUserEvent>(_onMuteUser);
    on<ReportUserEvent>(_onReportUser);
    on<BlockUserEvent>(_onBlockUser);
  }

  @override
  void onError(Object error, StackTrace stackTrace) {
    // Log error for debugging
    print('UnifiedProfileBloc Error: $error');
    print('StackTrace: $stackTrace');
    super.onError(error, stackTrace);
  }

  final UserRepository _userRepository;
  final BubbleRepository _bubbleRepository;
  final ContactRequestRepository _contactRequestRepository;
  final AuthBloc _authBloc;
  final RequestStateManager _requestStateManager;

  String? get _currentUserId => _authBloc.state.userId;

  /// Clears request tracking for a specific user's requests
  /// This now delegates to the RequestStateManager for persistent state management
  Future<void> _clearRequestTracking(String targetUserId) async {
    final currentUserId = _currentUserId;
    if (currentUserId != null) {
      // Clear from persistent cache
      await _requestStateManager.markRequestCompleted(
        currentUserId: currentUserId,
        otherUserId: targetUserId,
        requestType: RequestType.contact,
      );
      await _requestStateManager.markRequestCompleted(
        currentUserId: currentUserId,
        otherUserId: targetUserId,
        requestType: RequestType.bubbleStart,
      );
      await _requestStateManager.markRequestCompleted(
        currentUserId: currentUserId,
        otherUserId: targetUserId,
        requestType: RequestType.bubbleInvite,
      );
      await _requestStateManager.markRequestCompleted(
        currentUserId: currentUserId,
        otherUserId: targetUserId,
        requestType: RequestType.bubbleJoin,
      );
    }
  }

  Future<void> _onLoadUnifiedProfile(
    LoadUnifiedProfile event,
    Emitter<UnifiedProfileState> emit,
  ) async {
    print('🔄 UnifiedProfileBloc._onLoadUnifiedProfile: Loading profile for ${event.userId}');
    emit(UnifiedProfileLoading());

    try {
      final currentUserId = _currentUserId;
      if (currentUserId == null) {
        print('❌ UnifiedProfileBloc._onLoadUnifiedProfile: User not authenticated');
        emit(const UnifiedProfileError('User not authenticated'));
        return;
      }

      print('👤 UnifiedProfileBloc._onLoadUnifiedProfile: Current user: $currentUserId, Target user: ${event.userId}');

      // Initialize request state manager for the current user
      await _requestStateManager.initializeForUser(userId: currentUserId);

      // Get the target user's enhanced profile (includes relationship data)
      final user = await _userRepository.getEnhancedUser(event.userId);
      if (user == null) {
        emit(const UnifiedProfileError('User not found'));
        return;
      }

      print('🔍 UnifiedProfileBloc.LoadProfile: Loaded enhanced user with ${user.pendingSentBubbleRequestUserIds?.length ?? 0} pending sent bubble requests');

      // Cache the enhanced profile data for persistence
      await _requestStateManager.initializeForUser(userId: event.userId);

      // Get mutual friends and contacts
      final mutualFriends = await _userRepository.getMutualFriends(currentUserId, event.userId);
      final mutualContacts = await _userRepository.getMutualContacts(currentUserId, event.userId);

      // Determine relationship type
      final relationshipType = await _determineRelationshipType(currentUserId, event.userId);

      // -------------------------------------------------------------
      // Fetch bubble members for the target user if they are in a bubble
      // -------------------------------------------------------------
      List<BubbleMember> bubbleMembers = const [];
      if (user.bubbleId != null) {
        final bubbleIdResult = BubbleId.create(user.bubbleId!);
        if (bubbleIdResult.isSuccess) {
          final membersResult = await _bubbleRepository.getBubbleMembers(bubbleIdResult.data);
          if (membersResult.isSuccess) {
            bubbleMembers = membersResult.data
                .map((u) => BubbleMember(
                      id: u.id,
                      bubbleId: user.bubbleId!,
                      userId: u.id,
                      joinedAt: DateTime.now(),
                      user: u,
                      name: u.fullName,
                      profilePicUrl: u.profilePictureUrl,
                    ))
                .toList();
          }
        }
      }

      // Create enhanced user model with mutual connections and profile page actions
      final enhancedUser = await _enhanceUserWithProfileData(
        user,
        relationshipType,
        mutualFriends,
        mutualContacts,
        currentUserId,
      );

      // Check for pending requests before emitting normal loaded state
      print('🔍 UnifiedProfileBloc._onLoadUnifiedProfile: Checking for pending requests...');
      final pendingRequestState = await _checkForPendingRequests(
        currentUserId,
        event.userId,
        enhancedUser,
        relationshipType,
        bubbleMembers,
      );

      if (pendingRequestState != null) {
        print('📤 UnifiedProfileBloc._onLoadUnifiedProfile: Found pending request state: ${pendingRequestState.runtimeType}');
        emit(pendingRequestState);
      } else {
        print('📥 UnifiedProfileBloc._onLoadUnifiedProfile: No pending requests, emitting normal loaded state');
        emit(UnifiedProfileLoaded(
          user: enhancedUser,
          relationshipType: relationshipType,
          currentUserId: currentUserId,
          bubbleMembers: bubbleMembers,
        ));
      }
    } on Exception catch (e) {
      emit(UnifiedProfileError('Failed to load profile: ${e.toString()}'));
    }
  }

  /// Checks for pending requests and returns appropriate request sent state if found
  Future<UnifiedProfileState?> _checkForPendingRequests(
    String currentUserId,
    String targetUserId,
    UserModel enhancedUser,
    RelationshipType relationshipType,
    List<BubbleMember> bubbleMembers,
  ) async {
    try {
      // Check local state first for contact requests
      if (_sentContactRequests.containsKey(targetUserId)) {
        return UnifiedProfileContactRequestSent(
          user: enhancedUser,
          relationshipType: relationshipType,
          currentUserId: currentUserId,
          bubbleMembers: bubbleMembers,
        );
      }

      // Check backend for pending sent contact requests
      print('🔍 UnifiedProfileBloc._checkForPendingRequests: Checking backend for sent requests to $targetUserId');
      final sentRequestsResult = await _contactRequestRepository.getPendingSentRequests();
      if (sentRequestsResult.isSuccess) {
        final sentRequests = sentRequestsResult.data;
        print('🔍 UnifiedProfileBloc._checkForPendingRequests: Found ${sentRequests.length} sent requests');
        final hasPendingRequest = sentRequests.any((request) => request.receiverId == targetUserId);

        if (hasPendingRequest) {
          print('🔍 UnifiedProfileBloc._checkForPendingRequests: Found pending sent request to $targetUserId');
          // Update local state to track this request
          _sentContactRequests[targetUserId] = 'pending';

          return UnifiedProfileContactRequestSent(
            user: enhancedUser,
            relationshipType: RelationshipType.contactRequestSent,
            currentUserId: currentUserId,
            bubbleMembers: bubbleMembers,
          );
        }
      } else {
        print('🔍 UnifiedProfileBloc._checkForPendingRequests: Failed to get sent requests: ${sentRequestsResult.error}');
      }

      // Check backend for pending bubble requests using enhanced user profile data FIRST (source of truth)
      print('🔍 UnifiedProfileBloc._checkForPendingRequests: Checking backend for bubble requests to $targetUserId');

      // Get current user's enhanced profile to check sent bubble requests
      final currentUser = await _userRepository.getEnhancedUser(currentUserId);

      // DEBUG: Log the complete enhanced user data
      print('🔍 DEBUG: Enhanced user data for $currentUserId:');
      print('  - pendingSentBubbleRequestUserIds: ${currentUser?.pendingSentBubbleRequestUserIds}');
      print('  - pendingSentBubbleStartRequestUserIds: ${currentUser?.pendingSentBubbleStartRequestUserIds}');
      print('  - pendingSentBubbleInviteRequestUserIds: ${currentUser?.pendingSentBubbleInviteRequestUserIds}');
      print('  - pendingReceivedBubbleStartRequestUserIds: ${currentUser?.pendingReceivedBubbleStartRequestUserIds}');

      // Check for pending bubble start requests (the specific field the backend sends)
      if (currentUser?.pendingSentBubbleStartRequestUserIds?.contains(targetUserId) == true) {
        print('🔍 UnifiedProfileBloc._checkForPendingRequests: ✅ Found pending sent bubble START request from current user to $targetUserId');

        // Synchronize local state with backend state
        if (!_sentBubbleRequests.containsKey(targetUserId)) {
          _sentBubbleRequests[targetUserId] = {
            'requestId': 'backend_tracked', // Placeholder since we don't have the actual request ID
            'type': 'start', // Start request type
          };
        }

        return UnifiedProfileBubbleRequestSent(
          user: enhancedUser,
          relationshipType: relationshipType,
          currentUserId: currentUserId,
          requestType: BubbleRequestType.startBubbleTogether,
          bubbleMembers: bubbleMembers,
        );
      } else {
        print('🔍 UnifiedProfileBloc._checkForPendingRequests: ❌ NO pending bubble start request found from $currentUserId to $targetUserId');
      }

      // If backend shows no pending bubble requests, clear any stale local state
      if (_sentBubbleRequests.containsKey(targetUserId)) {
        print('🔍 UnifiedProfileBloc._checkForPendingRequests: Clearing stale local bubble request state for $targetUserId');
        _sentBubbleRequests.remove(targetUserId);
      }

      // Check backend for contact requests
      final sentContactRequestsResult = await _contactRequestRepository.getPendingSentRequests();

      if (sentContactRequestsResult.isSuccess) {
        final sentRequests = sentContactRequestsResult.data;
        final pendingRequest = sentRequests.where((request) =>
          request.receiverId == targetUserId && request.status == ContactRequestStatus.pending
        ).firstOrNull;

        if (pendingRequest != null) {
          // Update local state to track this request
          _sentContactRequests[targetUserId] = pendingRequest.id;

          return UnifiedProfileContactRequestSent(
            user: enhancedUser,
            relationshipType: relationshipType,
            currentUserId: currentUserId,
            bubbleMembers: bubbleMembers,
          );
        }
      }

      return null; // No pending requests found
    } on Exception catch (e) {
      // If checking for pending requests fails, just return null to show normal state
      print('Error checking pending requests: $e');
      return null;
    }
  }

  Future<void> _onSendContactRequest(
    SendContactRequest event,
    Emitter<UnifiedProfileState> emit,
  ) async {
    try {
      final currentUserId = _currentUserId;
      if (currentUserId == null) {
        emit(const UnifiedProfileError('User not authenticated'));
        return;
      }

      // Get current state to preserve user data
      if (state is! UnifiedProfileLoaded) {
        emit(const UnifiedProfileError('Profile not loaded'));
        return;
      }

      final currentState = state as UnifiedProfileLoaded;

      await _userRepository.sendContactRequest(
        fromUserId: currentUserId,
        toUserId: event.targetUserId,
      );

      // Update request state manager
      await _requestStateManager.onRequestSent(
        currentUserId: currentUserId,
        targetUserId: event.targetUserId,
        requestType: RequestType.contact,
      );

      // Track the sent request in local state (for backward compatibility)
      _sentContactRequests[event.targetUserId] = 'pending';

      // Emit contact request sent state instead of reloading
      emit(UnifiedProfileContactRequestSent(
        user: currentState.user,
        relationshipType: currentState.relationshipType,
        currentUserId: currentUserId,
        bubbleMembers: currentState.bubbleMembers,
      ));
    } on Exception catch (e) {
      emit(UnifiedProfileError('Failed to send contact request: ${e.toString()}'));
    }
  }



  Future<void> _onSendBubbleRequest(
    SendBubbleRequestEvent event,
    Emitter<UnifiedProfileState> emit,
  ) async {
    try {
      print('🚀 UnifiedProfileBloc._onSendBubbleRequest: Starting bubble request');
      print('  - Request type: ${event.requestType}');
      print('  - Target user: ${event.targetUserId}');

      final currentUserId = _currentUserId;
      if (currentUserId == null) {
        print('❌ UnifiedProfileBloc._onSendBubbleRequest: User not authenticated');
        emit(const UnifiedProfileError('User not authenticated'));
        return;
      }

      // Implement bubble request logic based on request type
      String requestType;
      String? bubbleId;

      switch (event.requestType) {
        case BubbleRequestType.startBubbleTogether:
          requestType = 'start';
          // For start requests, we don't need an existing bubble ID
          break;
        case BubbleRequestType.inviteToOwnBubble:
          requestType = 'invite';
          // Get current user's bubble ID
          final currentUser = await _userRepository.getUserById(currentUserId);
          bubbleId = currentUser?.bubbleId;
          break;
        case BubbleRequestType.requestToJoinBubble:
          requestType = 'join';
          // Get target user's bubble ID
          final targetUser = await _userRepository.getUserById(event.targetUserId);
          bubbleId = targetUser?.bubbleId;
          break;
      }

      print('🔄 UnifiedProfileBloc._onSendBubbleRequest: Sending request to backend');
      print('  - Request type: $requestType');
      print('  - Bubble ID: $bubbleId');
      print('  - From: $currentUserId');
      print('  - To: ${event.targetUserId}');

      // Create the bubble request
      final result = await _bubbleRepository.createRequest(
        bubbleId: bubbleId ?? '', // Empty string for start requests
        targetId: event.targetUserId,
        type: requestType,
        message: 'Bubble request from unified profile',
      );

      if (result.isSuccess) {
        print('✅ UnifiedProfileBloc._onSendBubbleRequest: Request sent successfully');
        print('  - Request ID: ${result.data.id}');

        // Update request state manager
        final requestTypeEnum = _mapBubbleRequestType(event.requestType);
        await _requestStateManager.onRequestSent(
          currentUserId: currentUserId,
          targetUserId: event.targetUserId,
          requestType: requestTypeEnum,
        );

        // Track the sent request in local state (for backward compatibility)
        _sentBubbleRequests[event.targetUserId] = {
          'requestId': result.data.id,
          'type': requestType,
        };

        print('📝 UnifiedProfileBloc._onSendBubbleRequest: Updated local state');
        print('  - Local state now contains: $_sentBubbleRequests');

        // Get current state to preserve user data
        if (state is! UnifiedProfileLoaded) {
          print('❌ UnifiedProfileBloc._onSendBubbleRequest: Profile not loaded');
          emit(const UnifiedProfileError('Profile not loaded'));
          return;
        }

        final currentState = state as UnifiedProfileLoaded;

        print('🎯 UnifiedProfileBloc._onSendBubbleRequest: Emitting UnifiedProfileBubbleRequestSent state');
        // Emit bubble request sent state instead of reloading
        emit(UnifiedProfileBubbleRequestSent(
          user: currentState.user,
          relationshipType: currentState.relationshipType,
          currentUserId: currentUserId,
          requestType: event.requestType,
          bubbleMembers: currentState.bubbleMembers,
        ));
      } else {
        print('❌ UnifiedProfileBloc._onSendBubbleRequest: Request failed');
        print('  - Error: ${result.error}');
        emit(UnifiedProfileError('Failed to send bubble request: ${result.error}'));
      }
    } on Exception catch (e) {
      emit(UnifiedProfileError('Failed to send bubble request: ${e.toString()}'));
    }
  }

  Future<void> _onUnblockUser(
    UnblockUser event,
    Emitter<UnifiedProfileState> emit,
  ) async {
    try {
      final currentUserId = _currentUserId;
      if (currentUserId == null) {
        emit(const UnifiedProfileError('User not authenticated'));
        return;
      }

      // Unblock the user using the repository
      final success = await _userRepository.unblockUser(
        userId: currentUserId,
        targetUserId: event.userId,
      );

      if (success) {
        emit(UnifiedProfileActionSuccess(
          message: 'User unblocked successfully',
          user: (state as UnifiedProfileLoaded).user,
          relationshipType: RelationshipType.none,
          currentUserId: currentUserId,
          bubbleMembers: (state as UnifiedProfileLoaded).bubbleMembers,
        ));

        // Reload the profile to get updated state
        add(LoadUnifiedProfile(userId: event.userId));
      } else {
        emit(const UnifiedProfileError('Failed to unblock user'));
      }
    } on Exception catch (e) {
      emit(UnifiedProfileError('Failed to unblock user: ${e.toString()}'));
    }
  }

  Future<void> _onUnfriendUser(
    UnfriendUserEvent event,
    Emitter<UnifiedProfileState> emit,
  ) async {
    try {
      final currentUserId = _currentUserId;
      if (currentUserId == null) {
        emit(const UnifiedProfileError('User not authenticated'));
        return;
      }

      // Remove contact/friend using the repository
      final success = await _userRepository.removeContact(
        userId: currentUserId,
        contactId: event.targetUserId,
      );

      if (success) {
        emit(UnifiedProfileActionSuccess(
          message: 'Contact removed successfully',
          user: (state as UnifiedProfileLoaded).user,
          relationshipType: RelationshipType.none,
          currentUserId: currentUserId,
          bubbleMembers: (state as UnifiedProfileLoaded).bubbleMembers,
        ));

        // Reload the profile to get updated state
        add(LoadUnifiedProfile(userId: event.targetUserId));
      } else {
        emit(const UnifiedProfileError('Failed to remove contact'));
      }
    } on Exception catch (e) {
      emit(UnifiedProfileError('Failed to unfriend user: ${e.toString()}'));
    }
  }

  Future<void> _onMuteUser(
    MuteUserEvent event,
    Emitter<UnifiedProfileState> emit,
  ) async {
    try {
      final currentUserId = _currentUserId;
      if (currentUserId == null) {
        emit(const UnifiedProfileError('User not authenticated'));
        return;
      }

      // Mute user functionality - this would need to be implemented in UserRepository
      // For now, we'll simulate success and show appropriate message
      emit(UnifiedProfileActionSuccess(
        message: 'User muted successfully',
        user: (state as UnifiedProfileLoaded).user,
        relationshipType: (state as UnifiedProfileLoaded).relationshipType,
        currentUserId: currentUserId,
        bubbleMembers: (state as UnifiedProfileLoaded).bubbleMembers,
      ));

      // Reload the profile to get updated state
      add(LoadUnifiedProfile(userId: event.targetUserId));
    } on Exception catch (e) {
      emit(UnifiedProfileError('Failed to mute user: ${e.toString()}'));
    }
  }

  Future<void> _onReportUser(
    ReportUserEvent event,
    Emitter<UnifiedProfileState> emit,
  ) async {
    try {
      final currentUserId = _currentUserId;
      if (currentUserId == null) {
        emit(const UnifiedProfileError('User not authenticated'));
        return;
      }

      // Report the user using the repository
      final success = await _userRepository.reportUser(
        reportedUserId: event.targetUserId,
        reason: event.reason,
        category: 'user_report',
      );

      if (success) {
        emit(UnifiedProfileActionSuccess(
          message: 'User reported successfully',
          user: (state as UnifiedProfileLoaded).user,
          relationshipType: (state as UnifiedProfileLoaded).relationshipType,
          currentUserId: currentUserId,
          bubbleMembers: (state as UnifiedProfileLoaded).bubbleMembers,
        ));

        // Reload the profile to get updated state
        add(LoadUnifiedProfile(userId: event.targetUserId));
      } else {
        emit(const UnifiedProfileError('Failed to report user'));
      }
    } on Exception catch (e) {
      emit(UnifiedProfileError('Failed to report user: ${e.toString()}'));
    }
  }

  Future<void> _onBlockUser(
    BlockUserEvent event,
    Emitter<UnifiedProfileState> emit,
  ) async {
    try {
      final currentUserId = _currentUserId;
      if (currentUserId == null) {
        emit(const UnifiedProfileError('User not authenticated'));
        return;
      }

      // Block the user using the repository
      final success = await _userRepository.blockUser(
        userId: currentUserId,
        targetUserId: event.targetUserId,
      );

      if (success) {
        emit(UnifiedProfileActionSuccess(
          message: 'User blocked successfully',
          user: (state as UnifiedProfileLoaded).user,
          relationshipType: RelationshipType.blocked,
          currentUserId: currentUserId,
          bubbleMembers: (state as UnifiedProfileLoaded).bubbleMembers,
        ));

        // Reload the profile to get updated state
        add(LoadUnifiedProfile(userId: event.targetUserId));
      } else {
        emit(const UnifiedProfileError('Failed to block user'));
      }
    } on Exception catch (e) {
      emit(UnifiedProfileError('Failed to block user: ${e.toString()}'));
    }
  }

  /// Handles contact request acceptance notification from dialog
  Future<void> _onContactRequestAccepted(
    ContactRequestAcceptedEvent event,
    Emitter<UnifiedProfileState> emit,
  ) async {
    final currentUserId = _currentUserId;
    if (currentUserId != null) {
      // Update request state manager
      await _requestStateManager.onRequestAccepted(
        currentUserId: currentUserId,
        otherUserId: event.requesterId,
        requestType: RequestType.contact,
      );
    }

    _clearRequestTracking(event.requesterId);

    // Provide immediate optimistic update if we have current user data
    if (state is UnifiedProfileLoaded) {
      final currentState = state as UnifiedProfileLoaded;
      final updatedUser = currentState.user.copyWith(
        profilePageActionType: 'startBubbleTogether',
        profilePageButtonText: 'Start a bubble together',
        isProfilePageButtonEnabled: true,
      );

      emit(UnifiedProfileLoaded(
        user: updatedUser,
        relationshipType: RelationshipType.contact,
        currentUserId: currentState.currentUserId,
        bubbleMembers: currentState.bubbleMembers,
      ));
    }

    // Then reload to get the actual updated data from server
    add(LoadUnifiedProfile(userId: event.requesterId));
  }

  /// Handles contact request decline notification from dialog
  Future<void> _onContactRequestDeclined(
    ContactRequestDeclinedEvent event,
    Emitter<UnifiedProfileState> emit,
  ) async {
    final currentUserId = _currentUserId;
    if (currentUserId != null) {
      // Update request state manager
      await _requestStateManager.onRequestDeclined(
        currentUserId: currentUserId,
        otherUserId: event.requesterId,
        requestType: RequestType.contact,
      );
    }

    _clearRequestTracking(event.requesterId);

    // Provide immediate optimistic update if we have current user data
    if (state is UnifiedProfileLoaded) {
      final currentState = state as UnifiedProfileLoaded;
      final updatedUser = currentState.user.copyWith(
        profilePageActionType: 'sendContactRequest',
        profilePageButtonText: 'Send contact request',
        isProfilePageButtonEnabled: true,
      );

      emit(UnifiedProfileLoaded(
        user: updatedUser,
        relationshipType: RelationshipType.none,
        currentUserId: currentState.currentUserId,
        bubbleMembers: currentState.bubbleMembers,
      ));
    }

    // Then reload to get the actual updated data from server
    add(LoadUnifiedProfile(userId: event.requesterId));
  }

  /// Handles bubble request acceptance notification from dialog
  Future<void> _onBubbleRequestAccepted(
    BubbleRequestAcceptedEvent event,
    Emitter<UnifiedProfileState> emit,
  ) async {
    final currentUserId = _currentUserId;
    if (currentUserId != null) {
      // Update request state manager (assuming bubble start request for now)
      await _requestStateManager.onRequestAccepted(
        currentUserId: currentUserId,
        otherUserId: event.targetUserId,
        requestType: RequestType.bubbleStart,
      );
    }

    _clearRequestTracking(event.targetUserId);

    // Provide immediate optimistic update if we have current user data
    if (state is UnifiedProfileLoaded) {
      final currentState = state as UnifiedProfileLoaded;
      // Update button to reflect that they're now in a bubble together
      final updatedUser = currentState.user.copyWith(
        profilePageActionType: 'sendMessage',
        profilePageButtonText: 'Send message',
        isProfilePageButtonEnabled: true,
      );

      emit(UnifiedProfileLoaded(
        user: updatedUser,
        relationshipType: currentState.relationshipType, // Keep current relationship type for bubble actions
        currentUserId: currentState.currentUserId,
        bubbleMembers: currentState.bubbleMembers,
      ));
    }

    // Then reload to get the actual updated data from server
    add(LoadUnifiedProfile(userId: event.targetUserId));
  }

  /// Handles bubble request decline notification from dialog
  Future<void> _onBubbleRequestDeclined(
    BubbleRequestDeclinedEvent event,
    Emitter<UnifiedProfileState> emit,
  ) async {
    final currentUserId = _currentUserId;
    if (currentUserId != null) {
      // Update request state manager (assuming bubble start request for now)
      await _requestStateManager.onRequestDeclined(
        currentUserId: currentUserId,
        otherUserId: event.targetUserId,
        requestType: RequestType.bubbleStart,
      );
    }

    _clearRequestTracking(event.targetUserId);

    // Provide immediate optimistic update if we have current user data
    if (state is UnifiedProfileLoaded) {
      final currentState = state as UnifiedProfileLoaded;
      // Reset button to allow sending another bubble request
      final updatedUser = currentState.user.copyWith(
        profilePageActionType: 'startBubbleTogether',
        profilePageButtonText: 'Start a bubble together',
        isProfilePageButtonEnabled: true,
      );

      emit(UnifiedProfileLoaded(
        user: updatedUser,
        relationshipType: currentState.relationshipType, // Keep current relationship type for bubble actions
        currentUserId: currentState.currentUserId,
        bubbleMembers: currentState.bubbleMembers,
      ));
    }

    // Then reload to get the actual updated data from server
    add(LoadUnifiedProfile(userId: event.targetUserId));
  }

  /// Determines the relationship type between the current user and target user
  Future<RelationshipType> _determineRelationshipType(
    String currentUserId,
    String targetUserId,
  ) async {
    if (currentUserId == targetUserId) {
      return RelationshipType.self;
    }

    try {
      // Get current user to check relationships
      final currentUser = await _userRepository.getUserById(currentUserId);
      if (currentUser == null) {
        return RelationshipType.none;
      }

      // Check if target user is blocked
      if (currentUser.blockedUserIds.contains(targetUserId)) {
        return RelationshipType.blocked;
      }

      // Check if they are friends
      if (currentUser.friendIds.contains(targetUserId)) {
        return RelationshipType.friend;
      }

      // Check if they are contacts (check user model first, then repository)
      if (currentUser.contactIds.contains(targetUserId)) {
        return RelationshipType.contact;
      }

      // ADDITIONAL CHECK: Query the ContactsRepository directly for existing contact relationships
      // This is needed because the user model's contactIds might not be populated
      try {
        final contactsRepository = di.sl<ContactsRepository>();
        // Check if there's an existing contact relationship
        final contacts = await contactsRepository.getContacts();
        final existingContact = contacts.any((contact) => 
          contact.id == targetUserId && contact.relationshipType == RelationshipType.contact
        );
        if (existingContact) {
          print('🔍 UnifiedProfileBloc._determineRelationshipType: Found existing contact relationship with $targetUserId');
          return RelationshipType.contact;
        }
      } catch (e) {
        print('🔍 UnifiedProfileBloc._determineRelationshipType: Error checking contacts repository: $e');
        // Continue with other checks
      }

      // Check for pending sent contact requests
      print('🔍 UnifiedProfileBloc._determineRelationshipType: Checking for pending sent requests to $targetUserId');
      final sentRequestsResult = await _contactRequestRepository.getPendingSentRequests();
      if (sentRequestsResult.isSuccess) {
        final sentRequests = sentRequestsResult.data;
        print('🔍 UnifiedProfileBloc._determineRelationshipType: Found ${sentRequests.length} sent requests');
        final hasPendingSentRequest = sentRequests.any((request) => request.receiverId == targetUserId);

        if (hasPendingSentRequest) {
          print('🔍 UnifiedProfileBloc._determineRelationshipType: Found pending sent request to $targetUserId');
          return RelationshipType.contactRequestSent;
        }
      } else {
        print('🔍 UnifiedProfileBloc._determineRelationshipType: Failed to get sent requests: ${sentRequestsResult.error}');
      }

      // Check for pending received contact requests
      print('🔍 UnifiedProfileBloc._determineRelationshipType: Checking for pending received requests from $targetUserId');
      final receivedRequestsResult = await _contactRequestRepository.getPendingReceivedRequests();
      if (receivedRequestsResult.isSuccess) {
        final receivedRequests = receivedRequestsResult.data;
        print('🔍 UnifiedProfileBloc._determineRelationshipType: Found ${receivedRequests.length} received requests');
        final hasPendingReceivedRequest = receivedRequests.any((request) => request.senderId == targetUserId);

        if (hasPendingReceivedRequest) {
          print('🔍 UnifiedProfileBloc._determineRelationshipType: Found pending received request from $targetUserId');
          return RelationshipType.contactRequestReceived;
        }
      } else {
        print('🔍 UnifiedProfileBloc._determineRelationshipType: Failed to get received requests: ${receivedRequestsResult.error}');
      }

      // Check if they are in the same bubble (bubblers)
      if (currentUser.bubbleId != null) {
        final targetUser = await _userRepository.getUserById(targetUserId);
        if (targetUser?.bubbleId == currentUser.bubbleId) {
          return RelationshipType.bubbler;
        }
      }

      // Check for pending contact requests (legacy user model fields)
      if (currentUser.pendingSentContactRequestIds.contains(targetUserId)) {
        return RelationshipType.contactRequestSent;
      }
      if (currentUser.pendingReceivedContactRequestIds.contains(targetUserId)) {
        return RelationshipType.contactRequestReceived;
      }

      // Check for pending friend requests (auto-generated from bubble expiry)
      // Note: This would need to be implemented based on the friendship service
      // For now, we'll return none

      return RelationshipType.none;
    } on Exception {
      // If we can't determine the relationship, default to none
      return RelationshipType.none;
    }
  }

  /// Enhances the user model with profile page specific data
  Future<UserModel> _enhanceUserWithProfileData(
    UserModel user,
    RelationshipType relationshipType,
    List<UserModel> mutualFriends,
    List<UserModel> mutualContacts,
    String currentUserId,
  ) async {
    // Convert mutual friends and contacts to user IDs
    final mutualFriendIds = mutualFriends.map((u) => u.id).toList();
    final mutualContactIds = mutualContacts.map((u) => u.id).toList();

    // Determine profile page action and button text based on relationship
    final actionData = await _getProfilePageActionData(relationshipType, user, currentUserId);

    return user.copyWith(
      mutualFriends: mutualFriendIds,
      mutualContacts: mutualContactIds,
      profilePageActionType: actionData.actionType,
      profilePageButtonText: actionData.buttonText,
      isProfilePageButtonEnabled: actionData.isEnabled,
    );
  }

  /// Gets the profile page action data based on relationship type
  Future<({
    String? actionType,
    String buttonText,
    bool isEnabled,
  })> _getProfilePageActionData(
    RelationshipType relationshipType,
    UserModel targetUser,
    String currentUserId,
  ) async {
    switch (relationshipType) {
      case RelationshipType.self:
        return (
          actionType: null,
          buttonText: 'Edit profile',
          isEnabled: true,
        );

      case RelationshipType.none:
        return (
          actionType: ProfilePageActionType.sendContactRequest.name,
          buttonText: 'Send contact request',
          isEnabled: true,
        );

      case RelationshipType.contact:
        // For contacts, the action depends on bubble status
        return _getContactActionData(targetUser, currentUserId);

      case RelationshipType.friend:
        return (
          actionType: ProfilePageActionType.sendMessage.name,
          buttonText: 'Message',
          isEnabled: true,
        );

      case RelationshipType.bubbler:
        return (
          actionType: null,
          buttonText: 'Bubbler',
          isEnabled: false,
        );

      case RelationshipType.blocked:
        return (
          actionType: ProfilePageActionType.unblockUser.name,
          buttonText: 'Unblock',
          isEnabled: true,
        );

      case RelationshipType.contactRequestSent:
        return (
          actionType: null,
          buttonText: 'Contact request sent',
          isEnabled: false,
        );

      case RelationshipType.contactRequestReceived:
        return (
          actionType: null,
          buttonText: 'Contact request received',
          isEnabled: false,
        );

      case RelationshipType.friendRequestSent:
        return (
          actionType: null,
          buttonText: 'Friend request sent',
          isEnabled: false,
        );

      case RelationshipType.friendRequestReceived:
        return (
          actionType: null,
          buttonText: 'Friend request received',
          isEnabled: false,
        );
    }
  }

  /// Gets contact-specific action data based on bubble statuses
  /// This implements the complex logic from the contact, bubble and friends requests system
  Future<({
    String? actionType,
    String buttonText,
    bool isEnabled,
  })> _getContactActionData(UserModel targetUser, String currentUserId) async {
    try {
      // Get current user's data to check bubble status
      final currentUser = await _userRepository.getUserById(currentUserId);
      if (currentUser == null) {
        return (
          actionType: null,
          buttonText: 'Error',
          isEnabled: false,
        );
      }

      final currentUserInBubble = currentUser.bubbleId != null;
      final targetUserInBubble = targetUser.bubbleId != null;
      final sameUserBubble = currentUserInBubble &&
                            targetUserInBubble &&
                            currentUser.bubbleId == targetUser.bubbleId;

      // Case 1: Both users not in bubbles → Start Bubble Together
      if (!currentUserInBubble && !targetUserInBubble) {
        return (
          actionType: BubbleRequestType.startBubbleTogether.name,
          buttonText: 'Start a bubble together',
          isEnabled: true,
        );
      }

      // Case 2: Current user not in bubble, target user in bubble → Request to Join (Bubble Join Request Flow)
      if (!currentUserInBubble && targetUserInBubble) {
        // Check if target's bubble is full
        if (targetUser.bubbleId != null) {
          final bubbleIdResult = BubbleId.create(targetUser.bubbleId!);
          if (bubbleIdResult.isSuccess) {
            final targetBubbleResult = await _bubbleRepository.getBubble(bubbleIdResult.data);
            final isBubbleFull = targetBubbleResult.isSuccess &&
                                targetBubbleResult.data.isAtCapacity;

            return (
              actionType: BubbleRequestType.requestToJoinBubble.name,
              buttonText: isBubbleFull ? 'Bubble full' : 'Request to join bubble',
              isEnabled: !isBubbleFull,
            );
          }
        }

        // Fallback if bubble ID is null or invalid
        return (
          actionType: BubbleRequestType.requestToJoinBubble.name,
          buttonText: 'Request to join bubble',
          isEnabled: true,
        );
      }

      // Case 3: Current user in bubble, target user not in bubble → Invite to Bubble (Bubble Invite Request Flow)
      if (currentUserInBubble && !targetUserInBubble) {
        // Check if current user's bubble is full
        if (currentUser.bubbleId != null) {
          final bubbleIdResult = BubbleId.create(currentUser.bubbleId!);
          if (bubbleIdResult.isSuccess) {
            final currentBubbleResult = await _bubbleRepository.getBubble(bubbleIdResult.data);
            final isBubbleFull = currentBubbleResult.isSuccess &&
                                currentBubbleResult.data.isAtCapacity;

            return (
              actionType: BubbleRequestType.inviteToOwnBubble.name,
              buttonText: isBubbleFull ? 'Bubble full' : 'Invite to bubble',
              isEnabled: !isBubbleFull,
            );
          }
        }

        // Fallback if bubble ID is null or invalid
        return (
          actionType: BubbleRequestType.inviteToOwnBubble.name,
          buttonText: 'Invite to bubble',
          isEnabled: true,
        );
      }

      // Case 4: Both users in same bubble → Already bubblers (handled elsewhere)
      if (sameUserBubble) {
        return (
          actionType: null,
          buttonText: 'Bubbler',
          isEnabled: false,
        );
      }

      // Case 5: Both users in different bubbles → No action possible
      if (currentUserInBubble && targetUserInBubble) {
        return (
          actionType: null,
          buttonText: 'Contact',
          isEnabled: false,
        );
      }

      // Fallback case
      return (
        actionType: ProfilePageActionType.sendMessage.name,
        buttonText: 'Message',
        isEnabled: true,
      );
    } on Exception {
      // If we can't determine bubble status, default to message
      return (
        actionType: ProfilePageActionType.sendMessage.name,
        buttonText: 'Message',
        isEnabled: true,
      );
    }
  }

  /// Maps BubbleRequestType to RequestType for the RequestStateManager
  RequestType _mapBubbleRequestType(BubbleRequestType bubbleRequestType) {
    switch (bubbleRequestType) {
      case BubbleRequestType.startBubbleTogether:
        return RequestType.bubbleStart;
      case BubbleRequestType.inviteToOwnBubble:
        return RequestType.bubbleInvite;
      case BubbleRequestType.requestToJoinBubble:
        return RequestType.bubbleJoin;
    }
  }

  @override
  Future<void> close() {
    _requestStateManager.dispose();
    return super.close();
  }
}
