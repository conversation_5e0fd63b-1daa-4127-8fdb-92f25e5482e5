import '../models/notification_model.dart';

/// Factory class to create notifications of different types
class NotificationFactory {
  static String _generateId() =>
      DateTime.now().millisecondsSinceEpoch.toString();

  // --- Contact / Friend Requests ---

  /// User A asks User B to be their contact
  static Notification createContactRequestReceived({
    required String fromUserId,
    required String fromUserName,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.contactRequest,
    message: 'You received a contact request from $fromUserName.',
    description: 'Notifies when another user sends a contact request.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'fromUserId': fromUserId,
      'fromUserName': fromUserName,
      'type': 'contact_request_received',
    },
  );

  /// User B accepts User A's contact request
  static Notification createContactRequestAccepted({
    required String acceptedByUserId,
    required String acceptedByUserName,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.contactRequest,
    message: '$acceptedByUserName accepted your contact request.',
    description: 'Alerts when your contact request is accepted.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'acceptedByUserId': acceptedByUserId,
      'acceptedByUserName': acceptedByUserName,
      'type': 'contact_request_accepted',
    },
  );

  /// User B declines User A's contact request
  static Notification createContactRequestDeclined({
    required String declinedByUserId,
    required String declinedByUserName,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.contactRequest,
    message: '$declinedByUserName declined your contact request.',
    description: 'Alerts when your contact request is declined.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'declinedByUserId': declinedByUserId,
      'declinedByUserName': declinedByUserName,
      'type': 'contact_request_declined',
    },
  );



  // --- Bubble Invitations & Join Requests ---

  /// User A requests to start a bubble with User B
  static Notification createBubbleStartRequestReceived({
    required String fromUserId,
    required String fromUserName,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.bubbleInvite,
    message: '$fromUserName wants to start a bubble with you.',
    description: 'Notifies when someone wants to start a bubble together.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'fromUserId': fromUserId,
      'fromUserName': fromUserName,
      'type': 'bubble_start_request_received',
    },
  );

  /// User A invites User B to join Bubble C
  static Notification createBubbleInvitationReceived({
    required String fromUserId,
    required String fromUserName,
    required String bubbleId,
    required String bubbleName,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.bubbleInvite,
    message: "$fromUserName invited you to join the bubble '$bubbleName'.",
    description: "Notifies when invited to join another's bubble.",
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'fromUserId': fromUserId,
      'fromUserName': fromUserName,
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'type': 'bubble_invitation_received',
    },
  );

  /// User A requests to join Bubble B (owned by User C)
  static Notification createBubbleJoinRequestReceived({
    required String requestingUserId,
    required String requestingUserName,
    required String bubbleId,
    required String bubbleName,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.bubbleInvite,
    message: "$requestingUserName wants to join your bubble '$bubbleName'.",
    description: 'Notifies when someone wants to join your bubble.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'requestingUserId': requestingUserId,
      'requestingUserName': requestingUserName,
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'type': 'bubble_join_request_received',
    },
  );

  /// Bubble B (which User A requested to join) unanimously accepts User A's request
  static Notification createBubbleJoinRequestAccepted({
    required String bubbleId,
    required String bubbleName,
    required String acceptedUserId, // User A
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.bubbleInvite,
    message: "Your request to join the bubble '$bubbleName' was accepted!",
    description: 'Alerts when your bubble join request is accepted.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'acceptedUserId': acceptedUserId,
      'type': 'bubble_join_request_accepted',
    },
  );

  /// A member of Bubble B rejects User A's request to join
  static Notification createBubbleJoinRequestRejected({
    required String bubbleId,
    required String bubbleName,
    required String rejectedByUserId, // Member who rejected
    required String rejectedByUserName,
    required String requestingUserId, // User A
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.bubbleInvite,
    message:
        "Your request to join the bubble '$bubbleName' was rejected by $rejectedByUserName.",
    description:
        'Alerts when your bubble join request is rejected by a member.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'rejectedByUserId': rejectedByUserId,
      'rejectedByUserName': rejectedByUserName,
      'requestingUserId': requestingUserId,
      'type': 'bubble_join_request_rejected',
    },
  );

  /// User A's request to join Bubble B is declined by all members
  static Notification createBubbleJoinRequestDeclined({
    required String bubbleId,
    required String bubbleName,
    required String declinedUserId,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.bubbleInvite,
    message: "Your request to join the bubble '$bubbleName' was declined.",
    description: 'Alerts when your bubble join request is declined by all members.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'declinedUserId': declinedUserId,
      'type': 'bubble_join_request_declined',
    },
  );

  /// User A's invitation to Bubble B is accepted by User C
  static Notification createBubbleInvitationAccepted({
    required String bubbleId,
    required String bubbleName,
    required String acceptedUserId,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.bubbleInvite,
    message: "Your invitation to the bubble '$bubbleName' was accepted!",
    description: 'Alerts when your bubble invitation is accepted.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'acceptedUserId': acceptedUserId,
      'type': 'bubble_invitation_accepted',
    },
  );

  /// User A's invitation to Bubble B is declined by User C
  static Notification createBubbleInvitationDeclined({
    required String bubbleId,
    required String bubbleName,
    required String declinedUserId,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.bubbleInvite,
    message: "Your invitation to the bubble '$bubbleName' was declined.",
    description: 'Alerts when your bubble invitation is declined.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'declinedUserId': declinedUserId,
      'type': 'bubble_invitation_declined',
    },
  );

  /// Bubble B is about to expire (warning notification)
  static Notification createBubbleExpiryWarning({
    required String bubbleId,
    required String bubbleName,
    required int daysLeft,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.system,
    message: "Your bubble '$bubbleName' will expire in $daysLeft days.",
    description: 'Warns when a bubble is about to expire.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'daysLeft': daysLeft,
      'type': 'bubble_expiry_warning',
    },
  );

  /// Bubble B has expired
  static Notification createBubbleExpired({
    required String bubbleId,
    required String bubbleName,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.system,
    message: "Your bubble '$bubbleName' has expired.",
    description: 'Notifies when a bubble has expired.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'type': 'bubble_expired',
    },
  );

  /// User A joins Bubble B (current user is in Bubble B)
  static Notification createBubbleMemberJoined({
    required String joinedUserId,
    required String joinedUserName,
    required String bubbleId,
    required String bubbleName,
    required bool isNowFull, // New parameter to indicate if the bubble is full
  }) {
    var messageText = "$joinedUserName joined your bubble '$bubbleName'.";
    if (isNowFull) {
      messageText += ' The bubble is now full!';
    }
    return Notification(
      id: _generateId(),
      category: NotificationCategory.bubbleInvite,
      message: messageText,
      description:
          'Notifies when a new member joins your bubble. Indicates if bubble is full.',
      createdAt: DateTime.now(),
      isRead: false,
      payload: {
        'joinedUserId': joinedUserId,
        'joinedUserName': joinedUserName,
        'bubbleId': bubbleId,
        'bubbleName': bubbleName,
        'isNowFull': isNowFull, // Added to payload
        'type': 'bubble_member_joined',
      },
    );
  }

  // --- Bubble Management & Interaction ---

  /// User A wants to "votekick" User B from Bubble C
  static Notification createBubbleVotekickInitiated({
    required String initiatorUserId,
    required String initiatorUserName,
    required String targetUserId,
    required String targetUserName,
    required String bubbleId,
    required String bubbleName,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.system,
    message:
        "$initiatorUserName initiated a votekick for $targetUserName in '$bubbleName'.",
    description:
        'Notifies when a votekick is initiated against another member in the bubble.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'initiatorUserId': initiatorUserId,
      'initiatorUserName': initiatorUserName,
      'targetUserId': targetUserId,
      'targetUserName': targetUserName,
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'type': 'bubble_votekick_initiated',
    },
  );

  /// All users in Bubble C unanimously votekick User B
  static Notification createBubbleVotekickPassed({
    required String targetUserId,
    required String targetUserName,
    required String bubbleId,
    required String bubbleName,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.system,
    message:
        "$targetUserName has been removed from '$bubbleName' by unanimous vote.",
    description:
        'Notifies when a member is unanimously votekicked from the bubble.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'targetUserId': targetUserId,
      'targetUserName': targetUserName,
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'type': 'bubble_votekick_passed',
    },
  );

  // --- Bubble Messages & Calls ---

  /// User A sends a written message in Bubble B chat
  static Notification createBubbleChatMessageReceived({
    required String senderUserId,
    required String senderUserName,
    required String bubbleId,
    required String bubbleName,
    required String messagePreview, // Short preview of the message
    required String messageId,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.message,
    message:
        "New message in '$bubbleName' from $senderUserName: $messagePreview",
    description: 'Notifies for new written messages in a bubble chat.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'senderUserId': senderUserId,
      'senderUserName': senderUserName,
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'messageId': messageId,
      'messagePreview': messagePreview,
      'type': 'bubble_chat_message_received',
    },
  );

  /// User A sends a voice message in Bubble B chat
  static Notification createBubbleVoiceMessageReceived({
    required String senderUserId,
    required String senderUserName,
    required String bubbleId,
    required String bubbleName,
    required String voiceMessageId,
    required Duration duration,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.message,
    message: "$senderUserName sent a voice message in '$bubbleName'.",
    description: 'Notifies for new voice messages in a bubble chat.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'senderUserId': senderUserId,
      'senderUserName': senderUserName,
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'voiceMessageId': voiceMessageId,
      'duration': duration.inSeconds,
      'type': 'bubble_voice_message_received',
    },
  );

  /// User A sends a video message in Bubble B chat
  static Notification createBubbleVideoMessageReceived({
    required String senderUserId,
    required String senderUserName,
    required String bubbleId,
    required String bubbleName,
    required String videoMessageId,
    required Duration duration,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.message,
    message: "$senderUserName sent a video message in '$bubbleName'.",
    description: 'Notifies for new video messages in a bubble chat.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'senderUserId': senderUserId,
      'senderUserName': senderUserName,
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'videoMessageId': videoMessageId,
      'duration': duration.inSeconds,
      'type': 'bubble_video_message_received',
    },
  );

  /// User A is making an audio call to Bubble B
  static Notification createBubbleAudioCallIncoming({
    required String callerUserId,
    required String callerUserName,
    required String bubbleId,
    required String bubbleName,
    required String callId,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.call,
    message: "$callerUserName is starting an audio call in '$bubbleName'.",
    description: 'Alerts for an incoming audio call to the bubble.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'callerUserId': callerUserId,
      'callerUserName': callerUserName,
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'callId': callId,
      'type': 'bubble_audio_call_incoming',
    },
  );

  /// User A is making a video call to Bubble B
  static Notification createBubbleVideoCallIncoming({
    required String callerUserId,
    required String callerUserName,
    required String bubbleId,
    required String bubbleName,
    required String callId,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.call,
    message: "$callerUserName is starting a video call in '$bubbleName'.",
    description: 'Alerts for an incoming video call to the bubble.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'callerUserId': callerUserId,
      'callerUserName': callerUserName,
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'callId': callId,
      'type': 'bubble_video_call_incoming',
    },
  );

  /// User A wants to share their screen with Bubble B
  static Notification createBubbleScreenShareIncoming({
    required String fromUserId,
    required String fromUserName,
    required String bubbleId,
    required String bubbleName,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.call,
    message: "$fromUserName wants to share screen in '$bubbleName'.",
    description: 'Alerts for an incoming screen share to the bubble.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'fromUserId': fromUserId,
      'fromUserName': fromUserName,
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'type': 'bubble_screen_share_incoming',
    },
  );

  /// Audio/video/screen-sharing call is ongoing in Bubble A (current user is in Bubble A but not necessarily in the call yet)
  static Notification createBubbleCallInProgress({
    required String bubbleId,
    required String bubbleName,
    required List<String>
    participantNames, // Names of users currently in the call
  }) {
    final participantsText =
        participantNames.isNotEmpty
            ? " with ${participantNames.join(', ')}"
            : '';
    return Notification(
      id: _generateId(),
      category: NotificationCategory.call,
      message: "Call ongoing in '$bubbleName'$participantsText.",
      description:
          'Indicates an audio/video/screen-sharing call is ongoing in a bubble.',
      createdAt: DateTime.now(),
      isRead: false,
      payload: {
        'bubbleId': bubbleId,
        'bubbleName': bubbleName,
        'participantNames': participantNames,
        'type': 'bubble_call_in_progress',
      },
    );
  }

  /// An audio/video/screen-sharing call in Bubble B has ended
  static Notification createBubbleCallEnded({
    required String bubbleId,
    required String bubbleName,
    required DateTime callStartTime,
    required Duration callDuration,
    required String callId,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.call,
    message:
        "Call in '$bubbleName' ended. Duration: ${_formatDuration(callDuration)}.",
    description: 'Notifies when a call has ended.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'callStartTime': callStartTime.toIso8601String(),
      'callDurationSeconds': callDuration.inSeconds,
      'callId': callId,
      'type': 'bubble_call_ended',
    },
  );

  /// An audio/video/screen-sharing call took place in Bubble B while User C was not connected
  static Notification createBubbleMissedCall({
    required String bubbleId,
    required String bubbleName,
    required DateTime callTime, // When the call happened
    required String callId,
    required List<String> participantUserNames,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.call,
    message:
        "You missed a call with ${participantUserNames.join(', ')} in '$bubbleName' at ${_formatTime(callTime)}.",
    description:
        'Notifies about a call that occurred while the user was not connected.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'callTime': callTime.toIso8601String(),
      'participantUserNames': participantUserNames,
      'callId': callId,
      'type': 'bubble_missed_call',
    },
  );

  // --- Bubble Lifecycle ---

  static Notification _createPopReminderNotification({
    required NotificationCategory category,
    required String bubbleId,
    required String bubbleName,
    required String daysRemainingMessage, // e.g., "in less than 60 days"
    required String typeString,
  }) => Notification(
    id: _generateId(),
    category: category,
    message: "Your bubble '$bubbleName' will pop $daysRemainingMessage!",
    description: 'Reminder that a bubble is nearing its pop date.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'type': typeString,
    },
  );

  static Notification createBubblePopReminder60Days({
    required String bubbleId,
    required String bubbleName,
  }) => _createPopReminderNotification(
    category: NotificationCategory.reminder,
    bubbleId: bubbleId,
    bubbleName: bubbleName,
    daysRemainingMessage: 'in less than 60 days',
    typeString: 'bubble_pop_reminder_60_days',
  );

  static Notification createBubblePopReminder30Days({
    required String bubbleId,
    required String bubbleName,
  }) => _createPopReminderNotification(
    category: NotificationCategory.reminder,
    bubbleId: bubbleId,
    bubbleName: bubbleName,
    daysRemainingMessage: 'in less than 30 days',
    typeString: 'bubble_pop_reminder_30_days',
  );

  static Notification createBubblePopReminder20Days({
    required String bubbleId,
    required String bubbleName,
  }) => _createPopReminderNotification(
    category: NotificationCategory.reminder,
    bubbleId: bubbleId,
    bubbleName: bubbleName,
    daysRemainingMessage: 'in less than 20 days',
    typeString: 'bubble_pop_reminder_20_days',
  );

  static Notification createBubblePopReminder10Days({
    required String bubbleId,
    required String bubbleName,
  }) => _createPopReminderNotification(
    category: NotificationCategory.reminder,
    bubbleId: bubbleId,
    bubbleName: bubbleName,
    daysRemainingMessage: 'in less than 10 days',
    typeString: 'bubble_pop_reminder_10_days',
  );

  static Notification createBubblePopReminder7Days({
    required String bubbleId,
    required String bubbleName,
  }) => _createPopReminderNotification(
    category: NotificationCategory.reminder,
    bubbleId: bubbleId,
    bubbleName: bubbleName,
    daysRemainingMessage: 'in less than 7 days',
    typeString: 'bubble_pop_reminder_7_days',
  );

  static Notification createBubblePopReminder3Days({
    required String bubbleId,
    required String bubbleName,
  }) => _createPopReminderNotification(
    category: NotificationCategory.reminder,
    bubbleId: bubbleId,
    bubbleName: bubbleName,
    daysRemainingMessage: 'in less than 3 days',
    typeString: 'bubble_pop_reminder_3_days',
  );

  static Notification createBubblePopReminder24Hours({
    required String bubbleId,
    required String bubbleName,
  }) => _createPopReminderNotification(
    category: NotificationCategory.reminder,
    bubbleId: bubbleId,
    bubbleName: bubbleName,
    daysRemainingMessage: 'in less than 24 hours',
    typeString: 'bubble_pop_reminder_24_hours',
  );

  /// Bubble A bursts
  static Notification createBubblePopped({
    required String bubbleId,
    required String bubbleName,
    required DateTime popTime,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.system,
    message: "Oh no! Your bubble '$bubbleName' has popped.",
    description: 'Notification that a bubble has reached its end and burst.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'popTime': popTime.toIso8601String(),
      'type': 'bubble_popped',
    },
  );

  // --- Direct Friend Interactions ---

  /// Friend A sends User B a written message
  static Notification createFriendChatMessageReceived({
    required String friendUserId,
    required String friendUserName,
    required String messageId,
    required String messagePreview,
    required String conversationId,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.message,
    message: 'New message from $friendUserName: $messagePreview',
    description: 'Alerts for new direct written messages from a friend.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'friendUserId': friendUserId,
      'friendUserName': friendUserName,
      'messageId': messageId,
      'messagePreview': messagePreview,
      'conversationId': conversationId,
      'type': 'friend_chat_message_received',
    },
  );

  /// Friend A sends User B a direct message (alias for chat message)
  static Notification createFriendDirectMessageReceived({
    required String senderUserId,
    required String senderUserName,
    required String messageId,
    required String messagePreview,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.message,
    message: 'New message from $senderUserName: $messagePreview',
    description: 'Notification for a direct message from a friend.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'senderUserId': senderUserId,
      'senderUserName': senderUserName,
      'messageId': messageId,
      'messagePreview': messagePreview,
      'type': 'friend_direct_message_received',
    },
  );

  /// Friend A sends User B a voice message
  static Notification createFriendVoiceMessageReceived({
    required String senderUserId,
    required String senderUserName,
    required String voiceMessageId,
    required Duration duration,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.message,
    message: '$senderUserName sent you a voice message.',
    description: 'Alerts for new direct voice messages from a friend.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'senderUserId': senderUserId,
      'senderUserName': senderUserName,
      'voiceMessageId': voiceMessageId,
      'duration': duration.inSeconds,
      'type': 'friend_voice_message_received',
    },
  );

  /// Friend A sends User B a video message
  static Notification createFriendVideoMessageReceived({
    required String senderUserId,
    required String senderUserName,
    required String videoMessageId,
    required Duration duration,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.message,
    message: '$senderUserName sent you a video message.',
    description: 'Alerts for new direct video messages from a friend.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'senderUserId': senderUserId,
      'senderUserName': senderUserName,
      'videoMessageId': videoMessageId,
      'duration': duration.inSeconds,
      'type': 'friend_video_message_received',
    },
  );

  /// Friend A calls User B via audio
  static Notification createFriendAudioCallIncoming({
    required String callerUserId,
    required String callerUserName,
    required String callId,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.call,
    message: '$callerUserName is audio calling you.',
    description: 'Alerts for an incoming audio call from a friend.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'callerUserId': callerUserId,
      'callerUserName': callerUserName,
      'callId': callId,
      'type': 'friend_audio_call_incoming',
    },
  );

  /// Friend A calls User B via video
  static Notification createFriendVideoCallIncoming({
    required String callerUserId,
    required String callerUserName,
    required String callId,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.call,
    message: '$callerUserName is video calling you.',
    description: 'Alerts for an incoming video call from a friend.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'callerUserId': callerUserId,
      'callerUserName': callerUserName,
      'callId': callId,
      'type': 'friend_video_call_incoming',
    },
  );

  /// Friend A calls User B wanting to share their screen
  static Notification createFriendScreenShareIncoming({
    required String friendUserId,
    required String friendUserName,
    required String callId, // Or a separate shareId
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.call,
    message: '$friendUserName wants to share their screen with you.',
    description: 'Alerts for an incoming screen share from a friend.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'friendUserId': friendUserId,
      'friendUserName': friendUserName,
      'callId': callId,
      'type': 'friend_screen_share_incoming',
    },
  );

  /// Friend call ended notification
  static Notification createFriendCallEnded({
    required String friendUserId,
    required String friendUserName,
    required DateTime callStartTime,
    required Duration callDuration,
    required String callId,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.call,
    message: 'Call with $friendUserName ended.',
    description: 'Notification when a friend call ends.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'friendUserId': friendUserId,
      'friendUserName': friendUserName,
      'callStartTime': callStartTime.toIso8601String(),
      'callDurationSeconds': callDuration.inSeconds,
      'callId': callId,
      'type': 'friend_call_ended',
    },
  );

  /// Current user missed an audio, video, or screen-sharing call from a friend
  static Notification createFriendMissedCall({
    required String callerUserId,
    required String callerUserName,
    required DateTime callTime,
    required String callId,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.call,
    message: 'You missed a call from $callerUserName.',
    description:
        'Notifies when an audio, video, or screen-sharing call from a friend is missed.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'callerUserId': callerUserId,
      'callerUserName': callerUserName,
      'callTime': callTime.toIso8601String(),
      'callId': callId,
      'type': 'friend_missed_call',
    },
  );

  /// Alerts user to an ongoing direct call with a friend they are not connected to
  static Notification createFriendCallInProgress({
    required String friendUserId,
    required String friendUserName,
    // List<String> otherParticipantNames, // If group calls with friends become a thing
  }) {
    // String participantsText = otherParticipantNames.isNotEmpty
    //     ? " with ${otherParticipantNames.join(', ')}"
    //     : "";
    return Notification(
      id: _generateId(),
      category: NotificationCategory.call,
      // message: "$friendUserName is in an ongoing call$participantsText.",
      message: '$friendUserName is in an ongoing call.',
      description:
          "Notifies when a direct friend call is ongoing that the user isn't connected to.",
      createdAt: DateTime.now(),
      isRead: false,
      payload: {
        'friendUserId': friendUserId,
        'friendUserName': friendUserName,
        // 'otherParticipantIds': otherParticipantIds,
        'type': 'friend_call_in_progress',
      },
    );
  }

  // --- User Activity & Engagement ---

  static Notification _createInactiveNoBubbleNotification({
    required NotificationCategory category,
    required String message,
    required String typeString,
    required String durationIdentifier, // e.g., "12h", "1d"
  }) => Notification(
    id: _generateId(),
    category: category,
    message: message,
    description: 'Reminder after $durationIdentifier of not being in a bubble.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {'type': typeString, 'durationIdentifier': durationIdentifier},
  );

  static Notification createInactiveNoBubble12Hours() =>
      _createInactiveNoBubbleNotification(
        category: NotificationCategory.reminder,
        message: 'Feeling a bit quiet? Find a new bubble to join!',
        typeString: 'inactive_no_bubble_12_hours',
        durationIdentifier: '12 hours',
      );

  static Notification createInactiveNoBubble1Day() =>
      _createInactiveNoBubbleNotification(
        category: NotificationCategory.reminder,
        message: "It's been a day! Time to dive into a new bubble?",
        typeString: 'inactive_no_bubble_1_day',
        durationIdentifier: '1 day',
      );

  static Notification createInactiveNoBubble2Days() =>
      _createInactiveNoBubbleNotification(
        category: NotificationCategory.reminder,
        message: 'Missing out on bubble fun? Join or create one!',
        typeString: 'inactive_no_bubble_2_days',
        durationIdentifier: '2 days',
      );

  static Notification createInactiveNoBubble3Days() =>
      _createInactiveNoBubbleNotification(
        category: NotificationCategory.reminder,
        message: 'Your next great conversation is a bubble away!',
        typeString: 'inactive_no_bubble_3_days',
        durationIdentifier: '3 days',
      );

  static Notification createInactiveNoBubble7Days() =>
      _createInactiveNoBubbleNotification(
        category: NotificationCategory.reminder,
        message: 'Reconnect and share moments in a Hopen bubble!',
        typeString: 'inactive_no_bubble_7_days',
        durationIdentifier: '7 days',
      );

  // --- Keeping these as they seem generic enough or not covered by new list ---

  /// For general status updates. The message should be VERY specific and user-facing.
  static Notification createStatusUpdate({
    required String title,
    required String message,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.system,
    title: title,
    message: message,
    description: 'System status update notification.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {'type': 'status_update'},
  );

  /// Security alert notification
  static Notification createSecurityAlert({
    required String alertType,
    required String title,
    required String message,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.system,
    title: title,
    message: message,
    description: 'Security alert notification.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'alertType': alertType,
      'type': 'security_alert',
    },
  );

  /// App update notification
  static Notification createAppUpdate({
    required String version,
    required String title,
    required String message,
    required bool isRequired,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.system,
    title: title,
    message: message,
    description: 'App update notification.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'version': version,
      'isRequired': isRequired,
      'type': 'app_update',
    },
  );

  static Notification createMention({
    required String mentionedByUserId,
    required String mentionedByUserName,
    required String bubbleId,
    required String bubbleName,
    required String messageId,
  }) => Notification(
    id: _generateId(),
    category: NotificationCategory.message,
    message: "You were mentioned by $mentionedByUserName in '$bubbleName'.",
    description: 'Alerts when mentioned in a bubble message.',
    createdAt: DateTime.now(),
    isRead: false,
    payload: {
      'mentionedByUserId': mentionedByUserId,
      'mentionedByUserName': mentionedByUserName,
      'bubbleId': bubbleId,
      'bubbleName': bubbleName,
      'messageId': messageId,
      'type': 'mention',
    },
  );





  // Helper for formatting duration
  static String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    final twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    if (duration.inHours > 0) {
      return '${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds';
    }
    return '$twoDigitMinutes:$twoDigitSeconds';
  }

  // Helper for formatting time
  static String _formatTime(DateTime dateTime) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    return '${twoDigits(dateTime.hour)}:${twoDigits(dateTime.minute)}';
  }
}
