/// Abstract dialog service interface to handle request dialogs
/// This allows the provider layer to trigger dialogs without directly depending on the presentation layer
abstract class DialogService {
  /// Show a bubble join request dialog
  void showBubbleJoinRequest({
    required String requesterId,
    required String requesterName,
    required DateTime requestTimestamp,
    required String bubbleId,
    required String bubbleName,
    String? requesterUsername,
    String? requesterProfilePicUrl,
  });

  /// Show a bubble invite request dialog
  void showBubbleInviteRequest({
    required String inviterId,
    required String inviterName,
    required DateTime inviteTimestamp,
    required String bubbleId,
    required String bubbleName,
    String? inviterUsername,
    String? inviterProfilePicUrl,
  });

  /// Show a bubble propose request dialog
  void showBubbleProposeRequest({
    required String proposedMemberId,
    required String proposedMemberName,
    required String proposerId,
    required String proposerName,
    required DateTime proposeTimestamp,
    required String bubbleId,
    required String bubbleName,
    String? proposedMemberUsername,
    String? proposedMemberProfilePicUrl,
  });

  /// Show a bubble kickout request dialog
  void showBubbleKickoutRequest({
    required String requestId,
    required String targetMemberId,
    required String targetMemberName,
    required String requesterId,
    required String requesterName,
    required DateTime requestTimestamp,
    required String bubbleId,
    required String bubbleName,
    String? targetMemberUsername,
    String? targetMemberProfilePicUrl,
  });

  /// Show a bubble start request dialog
  void showBubbleStartRequest({
    required String requesterId,
    required String requesterName,
    required DateTime requestTimestamp,
    String? requesterUsername,
    String? requesterProfilePicUrl,
  });

  /// Show a friends choice dialog
  void showFriendsChoiceRequest({
    required String bubbleId,
    required String bubbleName,
    required List<Map<String, dynamic>> formerMembersData,
  });

  /// Show a contact request dialog
  void showContactRequest({required Map<String, dynamic> contactData});

  /// Show a friend request dialog (auto-generated from bubble expiry)
  void showFriendRequest({
    required String requestId,
    required String requesterId,
    required String requesterName,
    required DateTime requestTimestamp,
    required String sourceBubbleId,
    String? requesterUsername,
    String? requesterProfilePicUrl,
    String? bubbleName,
  });

  /// Show a contact request dialog with specific parameters
  Future<void> showContactRequestDialog({
    required String fromUserId,
    required String fromUserName,
  });

  /// Show a bubble invite dialog
  Future<void> showBubbleInviteDialog({
    required String fromUserId,
    required String fromUserName,
    required String bubbleId,
    required String bubbleName,
  });

  /// Show a bubble join request dialog with specific parameters
  Future<void> showBubbleJoinRequestDialog({
    required String requestingUserId,
    required String requestingUserName,
    required String bubbleId,
    required String bubbleName,
  });

  /// Show an incoming call dialog
  Future<void> showIncomingCallDialog({
    required String callerUserId,
    required String callerUserName,
    required String callId,
    String? bubbleId,
    String? bubbleName,
  });
}
