import 'dart:async';
import '../models/notification_model.dart';
import '../notification/notification_factory.dart';
import '../../../repositories/notification/notification_repository.dart';
import '../services/logging_service.dart';

/// Dialog event data class
class DialogEvent {
  final String type;
  final Map<String, dynamic> data;

  DialogEvent({required this.type, required this.data});
}

/// Central orchestrator for all notification logic
/// Coordinates notification storage and processing following clean architecture
class NotificationOrchestrator {
  final NotificationRepository _notificationRepository;
  final StreamController<DialogEvent> _dialogEventController = StreamController<DialogEvent>.broadcast();

  // Track processed notifications to prevent duplicates
  final Set<String> _processedNotifications = <String>{};

  // Track when notifications were processed for cleanup
  final Map<String, DateTime> _processedTimestamps = <String, DateTime>{};

  NotificationOrchestrator({
    required NotificationRepository notificationRepository,
  }) : _notificationRepository = notificationRepository;

  /// Stream of dialog events for the presentation layer to listen to
  Stream<DialogEvent> get dialogEventStream => _dialogEventController.stream;

  /// Dispose resources
  void dispose() {
    _dialogEventController.close();
    _processedNotifications.clear();
    _processedTimestamps.clear();
  }

  /// Process incoming notification data and route appropriately
  Future<void> processNotification(Map<String, dynamic> data) async {
    LoggingService.info('🔔 NotificationOrchestrator: processNotification called with data: $data');

    final type = data['type'] as String?;
    if (type == null) {
      LoggingService.warning('NotificationOrchestrator: Received notification without type: $data');
      return;
    }

    // Create unique notification key for deduplication
    final notificationKey = _createNotificationKey(type, data);

    // Check if this notification has already been processed
    if (_processedNotifications.contains(notificationKey)) {
      LoggingService.info('🔔 NotificationOrchestrator: Notification already processed, skipping duplicate: $notificationKey');
      return;
    }

    // Mark notification as processed
    _processedNotifications.add(notificationKey);
    _processedTimestamps[notificationKey] = DateTime.now();
    LoggingService.info('🔔 NotificationOrchestrator: Processing notification type: $type (key: $notificationKey)');

    // Clean up old processed notifications periodically
    _cleanupOldProcessedNotifications();

    try {
    // Create notification using factory
    final notification = _createNotificationFromData(type, data);
      if (notification == null) {
        LoggingService.warning('NotificationOrchestrator: Could not create notification for type: $type');
        // Remove from processed set since we couldn't process it
        _processedNotifications.remove(notificationKey);
        return;
      }

    // Store notification
    await _notificationRepository.saveNotification(notification);

      // Trigger immediate dialog for request types
      _triggerDialogForRequestTypes(type, data);

      LoggingService.info('NotificationOrchestrator: Notification processed and stored successfully (key: $notificationKey)');
    } catch (e, stackTrace) {
      LoggingService.error('NotificationOrchestrator: Error processing notification: $e', stackTrace: stackTrace);
      // Remove from processed set since processing failed
      _processedNotifications.remove(notificationKey);
    }
  }

  /// Create notification from incoming data using factory
  Notification? _createNotificationFromData(String type, Map<String, dynamic> data) {
    try {
    switch (type) {
      case 'contact_request_received':
        return NotificationFactory.createContactRequestReceived(
            fromUserId: (data['fromUserId'] ?? data['requester_id'] ?? '').toString(),
            fromUserName: (data['fromUserName'] ?? data['requester_name'] ?? '').toString(),
        );
      
      case 'contact_request_accepted':
        return NotificationFactory.createContactRequestAccepted(
          acceptedByUserId: (data['acceptedByUserId'] ?? '').toString(),
          acceptedByUserName: (data['acceptedByUserName'] ?? '').toString(),
        );
      
      case 'contact_request_declined':
        return NotificationFactory.createContactRequestDeclined(
          declinedByUserId: (data['declinedByUserId'] ?? '').toString(),
          declinedByUserName: (data['declinedByUserName'] ?? '').toString(),
        );

      case 'bubble_start_request_received':
        return NotificationFactory.createBubbleStartRequestReceived(
          fromUserId: (data['fromUserId'] ?? data['requester_id'] ?? '').toString(),
          fromUserName: (data['fromUserName'] ?? data['requester_name'] ?? '').toString(),
        );

      case 'bubble_invitation_received':
        return NotificationFactory.createBubbleInvitationReceived(
          fromUserId: (data['fromUserId'] ?? '').toString(),
          fromUserName: (data['fromUserName'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
        );

      case 'bubble_invite_request_received':
        return NotificationFactory.createBubbleInvitationReceived(
          fromUserId: (data['fromUserId'] ?? data['inviter_id'] ?? data['sender_id'] ?? '').toString(),
          fromUserName: (data['fromUserName'] ?? data['inviter_name'] ?? data['sender_name'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
        );
      
      case 'bubble_join_request_received':
        return NotificationFactory.createBubbleJoinRequestReceived(
          requestingUserId: (data['requestingUserId'] ?? '').toString(),
          requestingUserName: (data['requestingUserName'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
        );
      
      case 'bubble_join_request_accepted':
        return NotificationFactory.createBubbleJoinRequestAccepted(
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
          acceptedUserId: (data['acceptedUserId'] ?? '').toString(),
        );
      
        case 'bubble_join_request_declined':
          return NotificationFactory.createBubbleJoinRequestDeclined(
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
            declinedUserId: (data['declinedUserId'] ?? '').toString(),
        );
      
        case 'bubble_invitation_accepted':
          return NotificationFactory.createBubbleInvitationAccepted(
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
            acceptedUserId: (data['acceptedUserId'] ?? '').toString(),
        );

      case 'bubble_join_request_rejected':
        return NotificationFactory.createBubbleJoinRequestRejected(
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
          rejectedByUserId: (data['rejectedByUserId'] ?? data['rejected_by_user_id'] ?? '').toString(),
          rejectedByUserName: (data['rejectedByUserName'] ?? data['rejected_by_user_name'] ?? '').toString(),
          requestingUserId: (data['requestingUserId'] ?? data['requesting_user_id'] ?? '').toString(),
        );

      case 'bubble_member_joined':
        return NotificationFactory.createBubbleMemberJoined(
          joinedUserId: (data['newMemberUserId'] ?? data['new_member_user_id'] ?? data['joinedUserId'] ?? '').toString(),
          joinedUserName: (data['newMemberName'] ?? data['new_member_name'] ?? data['joinedUserName'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
          isNowFull: (data['isNowFull'] ?? data['is_now_full'] ?? false) as bool,
        );

      case 'bubble_votekick_initiated':
        return NotificationFactory.createBubbleVotekickInitiated(
          initiatorUserId: (data['initiatorUserId'] ?? data['initiator_user_id'] ?? '').toString(),
          initiatorUserName: (data['initiatorUserName'] ?? data['initiator_user_name'] ?? '').toString(),
          targetUserId: (data['targetUserId'] ?? data['target_user_id'] ?? '').toString(),
          targetUserName: (data['targetUserName'] ?? data['target_user_name'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
        );

      case 'bubble_votekick_passed':
        return NotificationFactory.createBubbleVotekickPassed(
          targetUserId: (data['targetUserId'] ?? data['target_user_id'] ?? '').toString(),
          targetUserName: (data['targetUserName'] ?? data['target_user_name'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
        );

      case 'bubble_chat_message_received':
        return NotificationFactory.createBubbleChatMessageReceived(
          senderUserId: (data['senderUserId'] ?? data['sender_user_id'] ?? '').toString(),
          senderUserName: (data['senderUserName'] ?? data['sender_user_name'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
          messageId: (data['messageId'] ?? data['message_id'] ?? '').toString(),
          messagePreview: (data['messagePreview'] ?? data['message_preview'] ?? '').toString(),
        );

      case 'bubble_voice_message_received':
        return NotificationFactory.createBubbleVoiceMessageReceived(
          senderUserId: (data['senderUserId'] ?? data['sender_user_id'] ?? '').toString(),
          senderUserName: (data['senderUserName'] ?? data['sender_user_name'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
          voiceMessageId: (data['voiceMessageId'] ?? data['voice_message_id'] ?? '').toString(),
          duration: Duration(seconds: (data['duration'] ?? 0) as int),
        );

      case 'bubble_video_message_received':
        return NotificationFactory.createBubbleVideoMessageReceived(
          senderUserId: (data['senderUserId'] ?? data['sender_user_id'] ?? '').toString(),
          senderUserName: (data['senderUserName'] ?? data['sender_user_name'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
          videoMessageId: (data['videoMessageId'] ?? data['video_message_id'] ?? '').toString(),
          duration: Duration(seconds: (data['duration'] ?? 0) as int),
        );

      case 'bubble_audio_call_incoming':
        return NotificationFactory.createBubbleAudioCallIncoming(
          callerUserId: (data['callerUserId'] ?? data['caller_user_id'] ?? '').toString(),
          callerUserName: (data['callerUserName'] ?? data['caller_user_name'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
          callId: (data['callId'] ?? data['call_id'] ?? '').toString(),
        );

      case 'bubble_video_call_incoming':
        return NotificationFactory.createBubbleVideoCallIncoming(
          callerUserId: (data['callerUserId'] ?? data['caller_user_id'] ?? '').toString(),
          callerUserName: (data['callerUserName'] ?? data['caller_user_name'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
          callId: (data['callId'] ?? data['call_id'] ?? '').toString(),
        );

      case 'bubble_screen_share_incoming':
        return NotificationFactory.createBubbleScreenShareIncoming(
          fromUserId: (data['sharerUserId'] ?? data['sharer_user_id'] ?? data['fromUserId'] ?? '').toString(),
          fromUserName: (data['sharerUserName'] ?? data['sharer_user_name'] ?? data['fromUserName'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
        );

      case 'bubble_call_in_progress':
        return NotificationFactory.createBubbleCallInProgress(
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
          participantNames: (data['participantNames'] ?? []) as List<String>,
        );

      case 'bubble_call_ended':
        return NotificationFactory.createBubbleCallEnded(
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
          callStartTime: DateTime.tryParse((data['callStartTime'] ?? data['timestamp'] ?? '').toString()) ?? DateTime.now(),
          callDuration: Duration(seconds: (data['callDurationSeconds'] ?? data['duration'] ?? 0) as int),
          callId: (data['callId'] ?? data['call_id'] ?? '').toString(),
        );

      case 'bubble_missed_call':
        return NotificationFactory.createBubbleMissedCall(
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
          callTime: DateTime.tryParse((data['callTime'] ?? data['timestamp'] ?? '').toString()) ?? DateTime.now(),
          participantUserNames: (data['participantUserNames'] ?? []) as List<String>,
          callId: (data['callId'] ?? data['call_id'] ?? '').toString(),
        );

      case 'bubble_pop_reminder_60_days':
        return NotificationFactory.createBubblePopReminder60Days(
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
        );

      case 'bubble_pop_reminder_30_days':
        return NotificationFactory.createBubblePopReminder30Days(
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
        );

      case 'bubble_pop_reminder_20_days':
        return NotificationFactory.createBubblePopReminder20Days(
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
        );

      case 'bubble_pop_reminder_10_days':
        return NotificationFactory.createBubblePopReminder10Days(
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
        );

      case 'bubble_pop_reminder_7_days':
        return NotificationFactory.createBubblePopReminder7Days(
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
        );

      case 'bubble_pop_reminder_3_days':
        return NotificationFactory.createBubblePopReminder3Days(
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
        );

      case 'bubble_pop_reminder_24_hours':
        return NotificationFactory.createBubblePopReminder24Hours(
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
        );

      case 'bubble_popped':
        return NotificationFactory.createBubblePopped(
          bubbleId: (data['bubbleId'] ?? data['bubble_id'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? data['bubble_name'] ?? '').toString(),
          popTime: DateTime.tryParse((data['popTime'] ?? data['pop_time'] ?? data['timestamp'] ?? '').toString()) ?? DateTime.now(),
        );

      case 'friend_direct_message_received':
        return NotificationFactory.createFriendDirectMessageReceived(
          senderUserId: (data['senderUserId'] ?? data['sender_user_id'] ?? '').toString(),
          senderUserName: (data['senderUserName'] ?? data['sender_user_name'] ?? '').toString(),
          messageId: (data['messageId'] ?? data['message_id'] ?? '').toString(),
          messagePreview: (data['messagePreview'] ?? data['message_preview'] ?? '').toString(),
        );

      case 'friend_voice_message_received':
        return NotificationFactory.createFriendVoiceMessageReceived(
          senderUserId: (data['senderUserId'] ?? data['sender_user_id'] ?? '').toString(),
          senderUserName: (data['senderUserName'] ?? data['sender_user_name'] ?? '').toString(),
          voiceMessageId: (data['voiceMessageId'] ?? data['voice_message_id'] ?? '').toString(),
          duration: Duration(seconds: (data['duration'] ?? 0) as int),
        );

      case 'friend_video_message_received':
        return NotificationFactory.createFriendVideoMessageReceived(
          senderUserId: (data['senderUserId'] ?? data['sender_user_id'] ?? '').toString(),
          senderUserName: (data['senderUserName'] ?? data['sender_user_name'] ?? '').toString(),
          videoMessageId: (data['videoMessageId'] ?? data['video_message_id'] ?? '').toString(),
          duration: Duration(seconds: (data['duration'] ?? 0) as int),
        );

      case 'friend_audio_call_incoming':
        return NotificationFactory.createFriendAudioCallIncoming(
          callerUserId: (data['callerUserId'] ?? data['caller_user_id'] ?? '').toString(),
          callerUserName: (data['callerUserName'] ?? data['caller_user_name'] ?? '').toString(),
          callId: (data['callId'] ?? data['call_id'] ?? '').toString(),
        );

      case 'friend_video_call_incoming':
        return NotificationFactory.createFriendVideoCallIncoming(
          callerUserId: (data['callerUserId'] ?? data['caller_user_id'] ?? '').toString(),
          callerUserName: (data['callerUserName'] ?? data['caller_user_name'] ?? '').toString(),
          callId: (data['callId'] ?? data['call_id'] ?? '').toString(),
        );

      case 'friend_call_in_progress':
        return NotificationFactory.createFriendCallInProgress(
          friendUserId: (data['callerUserId'] ?? data['caller_user_id'] ?? '').toString(),
          friendUserName: (data['callerUserName'] ?? data['caller_user_name'] ?? '').toString(),
        );

      case 'friend_call_ended':
        return NotificationFactory.createFriendCallEnded(
          friendUserId: (data['callerUserId'] ?? data['caller_user_id'] ?? '').toString(),
          friendUserName: (data['callerUserName'] ?? data['caller_user_name'] ?? '').toString(),
          callStartTime: DateTime.tryParse((data['callStartTime'] ?? data['timestamp'] ?? '').toString()) ?? DateTime.now(),
          callDuration: Duration(seconds: (data['callDurationSeconds'] ?? data['duration'] ?? 0) as int),
          callId: (data['callId'] ?? data['call_id'] ?? '').toString(),
        );

      case 'friend_missed_call':
        return NotificationFactory.createFriendMissedCall(
          callerUserId: (data['callerUserId'] ?? data['caller_user_id'] ?? '').toString(),
          callerUserName: (data['callerUserName'] ?? data['caller_user_name'] ?? '').toString(),
          callTime: DateTime.tryParse((data['callTime'] ?? data['timestamp'] ?? '').toString()) ?? DateTime.now(),
          callId: (data['callId'] ?? data['call_id'] ?? '').toString(),
        );

      case 'inactive_no_bubble_12_hours':
        return NotificationFactory.createInactiveNoBubble12Hours();

      case 'inactive_no_bubble_1_day':
        return NotificationFactory.createInactiveNoBubble1Day();

      case 'inactive_no_bubble_2_days':
        return NotificationFactory.createInactiveNoBubble2Days();

      case 'inactive_no_bubble_3_days':
        return NotificationFactory.createInactiveNoBubble3Days();

      case 'inactive_no_bubble_7_days':
        return NotificationFactory.createInactiveNoBubble7Days();

      case 'status_update':
        return NotificationFactory.createStatusUpdate(
          title: (data['title'] ?? '').toString(),
          message: (data['message'] ?? '').toString(),
        );

      case 'security_alert':
        return NotificationFactory.createSecurityAlert(
          alertType: (data['alertType'] ?? data['alert_type'] ?? '').toString(),
          title: (data['title'] ?? '').toString(),
          message: (data['message'] ?? '').toString(),
        );

      case 'app_update':
        return NotificationFactory.createAppUpdate(
          version: (data['version'] ?? '').toString(),
          title: (data['title'] ?? '').toString(),
          message: (data['message'] ?? '').toString(),
          isRequired: (data['isRequired'] ?? data['is_required'] ?? false) as bool,
        );

        case 'bubble_invitation_declined':
          return NotificationFactory.createBubbleInvitationDeclined(
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
            declinedUserId: (data['declinedUserId'] ?? '').toString(),
        );
      
        case 'bubble_expiry_warning':
          return NotificationFactory.createBubbleExpiryWarning(
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
            daysLeft: (data['daysLeft'] ?? 0) as int,
        );
      
        case 'bubble_expired':
          return NotificationFactory.createBubbleExpired(
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
        );
      
      case 'bubble_audio_call_incoming':
        return NotificationFactory.createBubbleAudioCallIncoming(
          callerUserId: (data['callerUserId'] ?? '').toString(),
          callerUserName: (data['callerUserName'] ?? '').toString(),
            callId: (data['callId'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
        );
      
      case 'bubble_video_call_incoming':
        return NotificationFactory.createBubbleVideoCallIncoming(
          callerUserId: (data['callerUserId'] ?? '').toString(),
          callerUserName: (data['callerUserName'] ?? '').toString(),
            callId: (data['callId'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
        );
      
      case 'friend_audio_call_incoming':
        return NotificationFactory.createFriendAudioCallIncoming(
          callerUserId: (data['callerUserId'] ?? data['caller_user_id'] ?? '').toString(),
          callerUserName: (data['callerUserName'] ?? data['caller_user_name'] ?? '').toString(),
          callId: (data['callId'] ?? data['call_id'] ?? '').toString(),
        );

      case 'friend_video_call_incoming':
        return NotificationFactory.createFriendVideoCallIncoming(
          callerUserId: (data['callerUserId'] ?? data['caller_user_id'] ?? '').toString(),
          callerUserName: (data['callerUserName'] ?? data['caller_user_name'] ?? '').toString(),
          callId: (data['callId'] ?? data['call_id'] ?? '').toString(),
        );
      
      // Add more cases as needed
      default:
          LoggingService.warning('NotificationOrchestrator: Unknown notification type: $type');
          return null;
      }
    } catch (e) {
      LoggingService.error('NotificationOrchestrator: Error creating notification for type $type: $e');
        return null;
    }
  }

  /// Get all notifications for user
  Future<List<Notification>> getNotifications() async {
    return await _notificationRepository.getNotifications();
  }

  /// Get notifications by category
  Future<List<Notification>> getNotificationsByCategory(NotificationCategory category) async {
    return await _notificationRepository.getNotificationsByCategory(category);
  }

  /// Get notifications by type
  Future<List<Notification>> getNotificationsByType(String type) async {
    return await _notificationRepository.getNotificationsByType(type);
  }

  /// Get unread notifications count
  Future<int> getUnreadCount() async {
    return await _notificationRepository.getUnreadCount();
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    await _notificationRepository.markAsRead(notificationId);
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    await _notificationRepository.markAllAsRead();
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    await _notificationRepository.deleteNotification(notificationId);
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    await _notificationRepository.clearAll();
  }

  /// Get notifications stream for real-time updates
  Stream<List<Notification>> notificationsStream() {
    return _notificationRepository.notificationsStream();
  }

  /// Get unread count stream for real-time updates
  Stream<int> unreadCountStream() {
    return _notificationRepository.unreadCountStream();
  }

  /// Clean up old processed notifications to prevent memory leaks
  void _cleanupOldProcessedNotifications() {
    final now = DateTime.now();
    final cutoff = now.subtract(const Duration(hours: 1)); // Keep for 1 hour

    final keysToRemove = <String>[];
    for (final entry in _processedTimestamps.entries) {
      if (entry.value.isBefore(cutoff)) {
        keysToRemove.add(entry.key);
      }
    }

    for (final key in keysToRemove) {
      _processedNotifications.remove(key);
      _processedTimestamps.remove(key);
    }

    if (keysToRemove.isNotEmpty) {
      LoggingService.info('NotificationOrchestrator: Cleaned up ${keysToRemove.length} old processed notifications');
    }

    // Trigger cleanup every 100 processed notifications to prevent excessive memory usage
    if (_processedNotifications.length > 100) {
      LoggingService.info('NotificationOrchestrator: Triggering cleanup due to high notification count');
    }
  }

  /// Create unique notification key for deduplication
  String _createNotificationKey(String type, Map<String, dynamic> data) {
    switch (type) {
      case 'contact_request_received':
        final requesterId = data['requesterId'] ?? data['requester_id'] ?? data['fromUserId'] ?? '';
        return 'contact_request_$requesterId';

      case 'bubble_start_request_received':
        final requesterId = data['requesterId'] ?? data['requester_id'] ?? data['fromUserId'] ?? '';
        return 'bubble_start_request_$requesterId';

      case 'bubble_invite_request_received':
        final bubbleId = data['bubbleId'] ?? data['bubble_id'] ?? '';
        final inviterId = data['inviterId'] ?? data['inviter_id'] ?? data['fromUserId'] ?? data['sender_id'] ?? '';
        return 'bubble_invite_request_${bubbleId}_$inviterId';

      case 'bubble_join_request_dialog':
        final requestId = data['requestId'] ?? data['request_id'] ?? '';
        final requesterId = data['requesterId'] ?? data['requester_id'] ?? data['fromUserId'] ?? '';
        return 'bubble_join_request_${requestId}_$requesterId';

      case 'bubble_kickout_request_dialog':
        final requestId = data['requestId'] ?? data['request_id'] ?? '';
        return 'bubble_kickout_request_$requestId';

      case 'bubble_propose_request_dialog':
        final proposedMemberId = data['proposedMemberId'] ?? data['proposed_member_id'] ?? '';
        final proposerId = data['proposerId'] ?? data['proposer_id'] ?? data['fromUserId'] ?? '';
        return 'bubble_propose_request_${proposedMemberId}_$proposerId';

      case 'friends_choice_dialog':
        final bubbleId = data['bubbleId'] ?? data['bubble_id'] ?? '';
        return 'friends_choice_$bubbleId';

      default:
        // For other notification types, use type + timestamp or a generic ID
        final id = data['id'] ?? data['requestId'] ?? data['notificationId'] ?? DateTime.now().millisecondsSinceEpoch.toString();
        return '${type}_$id';
    }
  }

  /// Trigger immediate dialog display for request notification types
  void _triggerDialogForRequestTypes(String type, Map<String, dynamic> data) {
    LoggingService.info('🔔 NotificationOrchestrator: _triggerDialogForRequestTypes called with type: $type, data: $data');

    // Emit dialog event for the presentation layer to handle
    _dialogEventController.add(DialogEvent(type: type, data: data));

    LoggingService.info('🔔 NotificationOrchestrator: Dialog event emitted for type: $type');
  }
}