import 'package:flutter/material.dart';

import '../../statefulbusinesslogic/core/models/user_model.dart';
import '../../statefulbusinesslogic/core/services/dialog_service.dart';
import '../widgets/requests/bubble_invite_request_dialog.dart';
import '../widgets/requests/bubble_join_request_dialog.dart';
import '../widgets/requests/bubble_kickout_request_dialog.dart';
import '../widgets/requests/bubble_propose_request_dialog.dart';
import '../widgets/requests/bubble_start_request_dialog.dart';
import '../widgets/requests/contact_request_dialog.dart';
import '../widgets/requests/friend_request_dialog.dart';
import '../widgets/requests/friends_choice_dialog.dart';

/// Concrete implementation of the dialog service that uses Flutter widgets
class DialogServiceImpl implements DialogService {
  BuildContext? _context;

  /// Set the current context for showing dialogs
  void setContext(BuildContext context) {
    _context = context;
  }

  /// Clear the context when not needed
  void clearContext() {
    _context = null;
  }

  @override
  void showBubbleJoinRequest({
    required String requesterId,
    required String requesterName,
    required DateTime requestTimestamp, required String bubbleId, required String bubbleName, String? requesterUsername,
    String? requesterProfilePicUrl,
  }) {
    if (_context == null) return;

    try {
      BubbleJoinRequestDialog.show(
        _context!,
        requestId: '${requesterId}_${bubbleId}_${requestTimestamp.millisecondsSinceEpoch}',
        requesterId: requesterId,
        requesterName: requesterName,
        bubbleName: bubbleName,
        members: [], // Members will be fetched by the dialog itself
        bubbleId: bubbleId,
      );
    } catch (e) {
      debugPrint('Error showing bubble join request dialog: $e');
    }
  }

  @override
  void showBubbleInviteRequest({
    required String inviterId,
    required String inviterName,
    required DateTime inviteTimestamp, required String bubbleId, required String bubbleName, String? inviterUsername,
    String? inviterProfilePicUrl,
  }) {
    if (_context == null) return;

    try {
      BubbleInviteRequestDialog.show(
        _context!,
        bubbleName: bubbleName,
        members: [], // Members will be fetched by the dialog itself
        bubbleId: bubbleId,
      );
    } catch (e) {
      debugPrint('Error showing bubble invite request dialog: $e');
    }
  }

  @override
  void showBubbleProposeRequest({
    required String proposedMemberId,
    required String proposedMemberName,
    required String proposerId, required String proposerName, required DateTime proposeTimestamp, required String bubbleId, required String bubbleName, String? proposedMemberUsername,
    String? proposedMemberProfilePicUrl,
  }) {
    if (_context == null) return;

    try {
      BubbleProposeRequestDialog.show(
        _context!,
        bubbleName: bubbleName,
        members: [], // Members will be fetched by the dialog itself
        bubbleId: bubbleId,
        proposedMemberId: proposedMemberId,
      );
    } catch (e) {
      debugPrint('Error showing bubble propose request dialog: $e');
    }
  }

  @override
  void showBubbleKickoutRequest({
    required String requestId,
    required String targetMemberId,
    required String targetMemberName,
    required String requesterId,
    required String requesterName,
    required DateTime requestTimestamp,
    required String bubbleId,
    required String bubbleName,
    String? targetMemberUsername,
    String? targetMemberProfilePicUrl,
  }) {
    if (_context == null) return;

    try {
      BubbleKickoutRequestDialog.show(
        _context!,
        bubbleName: bubbleName,
        members: [], // Members will be fetched by the dialog itself
      );
    } catch (e) {
      debugPrint('Error showing bubble kickout request dialog: $e');
    }
  }

  @override
  void showBubbleStartRequest({
    required String requesterId,
    required String requesterName,
    required DateTime requestTimestamp, String? requesterUsername,
    String? requesterProfilePicUrl,
  }) {
    if (_context == null) return;

    try {
      BubbleStartRequestDialog.show(
        _context!,
        requesterId: requesterId,
        requesterName: requesterName,
        requesterUsername: requesterUsername,
        requesterProfilePicUrl: requesterProfilePicUrl,
        requestTimestamp: requestTimestamp,
      );
    } catch (e) {
      debugPrint('Error showing bubble start request dialog: $e');
    }
  }

  @override
  void showFriendsChoiceRequest({
    required String bubbleId,
    required String bubbleName,
    required List<Map<String, dynamic>> formerMembersData,
  }) {
    if (_context == null) return;

    try {
      final formerMembers = formerMembersData.map<UserModel>((memberData) {
        final name = memberData['name'] as String? ?? 'Unknown User';
        final nameParts = name.split(' ');
        
        return UserModel(
          id: memberData['userId'] as String? ?? memberData['id'] as String,
          email: memberData['email'] as String?,
          firstName: nameParts.isNotEmpty ? nameParts.first : null,
          lastName: nameParts.length > 1 ? nameParts.skip(1).join(' ') : null,
          username: memberData['username'] as String?,
          profilePictureUrl: memberData['avatarUrl'] as String?,
        );
      }).toList();

      FriendsChoiceDialog.show(
        _context!,
        bubbleId: bubbleId,
        bubbleName: bubbleName,
        formerMembers: formerMembers,
      );
    } catch (e) {
      debugPrint('Error showing friends choice dialog: $e');
    }
  }

  @override
  void showContactRequest({
    required Map<String, dynamic> contactData,
  }) {
    debugPrint('🔔 DialogService: showContactRequest called with data: $contactData');

    if (_context == null) {
      debugPrint('🔔 DialogService: ERROR - _context is null, cannot show dialog');
      return;
    }

    debugPrint('🔔 DialogService: _context is available, showing contact request dialog');
    try {
      ContactRequestDialog.show(
        _context!,
        requestId: contactData['requestId'] as String? ?? 'temp_request_id',
        requesterId: contactData['requesterId'] as String? ?? contactData['userId'] as String? ?? '',
        requesterName: contactData['requesterName'] as String? ?? contactData['name'] as String? ?? 'Unknown User',
        requesterUsername: contactData['requesterUsername'] as String? ?? contactData['username'] as String?,
        requesterProfilePicUrl: contactData['requesterProfilePicUrl'] as String? ?? contactData['profilePictureUrl'] as String?,
        requestTimestamp: contactData['requestTimestamp'] as DateTime? ?? DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error showing contact request dialog: $e');
    }
  }

  @override
  Future<void> showContactRequestDialog({
    required String fromUserId,
    required String fromUserName,
  }) async {
    if (_context == null) return;

    try {
      ContactRequestDialog.show(
        _context!,
        requestId: 'temp_request_id',
        requesterId: fromUserId,
        requesterName: fromUserName,
        requesterUsername: null,
        requesterProfilePicUrl: null,
        requestTimestamp: DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error showing contact request dialog: $e');
    }
  }

  @override
  Future<void> showBubbleInviteDialog({
    required String fromUserId,
    required String fromUserName,
    required String bubbleId,
    required String bubbleName,
  }) async {
    if (_context == null) return;

    try {
      BubbleInviteRequestDialog.show(
        _context!,
        bubbleName: bubbleName,
        members: [], // Members will be fetched by the dialog itself
        bubbleId: bubbleId,
      );
    } catch (e) {
      debugPrint('Error showing bubble invite dialog: $e');
    }
  }

  @override
  Future<void> showBubbleJoinRequestDialog({
    required String requestingUserId,
    required String requestingUserName,
    required String bubbleId,
    required String bubbleName,
  }) async {
    if (_context == null) return;

    try {
      BubbleJoinRequestDialog.show(
        _context!,
        requestId: '${requestingUserId}_${bubbleId}_${DateTime.now().millisecondsSinceEpoch}',
        requesterId: requestingUserId,
        requesterName: requestingUserName,
        bubbleName: bubbleName,
        members: [], // Members will be fetched by the dialog itself
        bubbleId: bubbleId,
      );
    } catch (e) {
      debugPrint('Error showing bubble join request dialog: $e');
    }
  }

  @override
  Future<void> showIncomingCallDialog({
    required String callerUserId,
    required String callerUserName,
    required String callId,
    String? bubbleId,
    String? bubbleName,
  }) async {
    if (_context == null) return;

    try {
      // For now, just show a simple dialog
      // In a real implementation, this would show a proper incoming call dialog
      showDialog(
        context: _context!,
        builder: (context) => AlertDialog(
          title: const Text('Incoming Call'),
          content: Text('Call from $callerUserName'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Decline'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Accept'),
            ),
          ],
        ),
      );
    } catch (e) {
      debugPrint('Error showing incoming call dialog: $e');
    }
  }

  @override
  void showFriendRequest({
    required String requestId,
    required String requesterId,
    required String requesterName,
    required DateTime requestTimestamp,
    required String sourceBubbleId,
    String? requesterUsername,
    String? requesterProfilePicUrl,
    String? bubbleName,
  }) {
    if (_context == null) return;

    try {
      FriendRequestDialog.show(
        _context!,
        requestId: requestId,
        requesterId: requesterId,
        requesterName: requesterName,
        requestTimestamp: requestTimestamp,
        sourceBubbleId: sourceBubbleId,
        requesterUsername: requesterUsername,
        requesterProfilePicUrl: requesterProfilePicUrl,
        bubbleName: bubbleName,
      );
    } catch (e) {
      debugPrint('Error showing friend request dialog: $e');
    }
  }
}