import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../statefulbusinesslogic/bloc/bubble/bubble_bloc.dart';
import '../../statefulbusinesslogic/bloc/bubble/bubble_event.dart';
import '../../statefulbusinesslogic/bloc/bubble/bubble_state.dart';
import '../../statefulbusinesslogic/core/models/bubble_entity.dart';

/// A custom styled dialog specifically for displaying bubble history.
///
/// It fetches and displays a list of past bubbles, showing details such as
/// the bubble name, completion status, duration, and members who completed it.
///
/// Key features include:
/// - Displaying completed members as clickable avatars in a horizontal row.
/// - Making member avatars clickable, navigating to their respective
///   profile pages (`UnifiedProfilePage`).
/// - Handling navigation bar visibility (hides on navigating to a profile,
///   restores on return).
/// - Responsive text sizing for various screen dimensions.
///
/// The dialog uses the main BubbleBloc to load bubble data and relies on
/// the new clean architecture with BubbleEntity.
class BubbleHistoryDialog extends StatelessWidget {
  const BubbleHistoryDialog({super.key});

  /// Static method to easily show the dialog.
  static Future<void> show(BuildContext context) => showDialog<void>(
    context: context,
    barrierColor: Colors.black.withValues(alpha: 0.85),
    builder: (dialogContext) => const BubbleHistoryDialog(),
  );

  // --- Responsive Helper Methods ---
  double _getTitleSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width < 360 ? 16.0 : (width < 600 ? 18.0 : 20.0);
  }

  double _getBodyTextSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width < 360 ? 12.0 : (width < 600 ? 14.0 : 16.0);
  }

  double _getSubtitleTextSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width < 360 ? 10.0 : (width < 600 ? 11.0 : 12.0);
  }

  double _getButtonTextSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width < 360 ? 14.0 : (width < 600 ? 16.0 : 18.0);
  }

  @override
  Widget build(BuildContext context) {
    // Load current bubble data when dialog opens
    context.read<BubbleBloc>().add(const LoadBubble());

    return AlertDialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
      titlePadding: const EdgeInsets.only(
        top: 24,
        left: 24,
        right: 24,
        bottom: 8,
      ),
      contentPadding: const EdgeInsets.symmetric(vertical: 12),
      title: Text(
        'Bubble History',
        textAlign: TextAlign.left,
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: _getTitleSize(context),
        ),
      ),
      content: BlocBuilder<BubbleBloc, BubbleState>(
        builder:
            (context, state) => state.when(
              onInitial: () => _buildEmptyHistory(context),
              onLoading: (operation, currentBubble) => _buildLoading(),
              onLoaded: (bubble) => _buildBubbleHistory(context, bubble),
              onError:
                  (error, previousBubble, operation) =>
                      _buildError(context, error.userMessage),
              onSuccess:
                  (operation, message, updatedBubble) =>
                      updatedBubble != null
                          ? _buildBubbleHistory(context, updatedBubble)
                          : _buildEmptyHistory(context),
            ),
      ),
      actionsAlignment: MainAxisAlignment.center,
      actions: [
        TextButton(
          child: Text(
            'Close',
            style: TextStyle(
              color: const Color(0xFF00FFFF),
              fontSize: _getButtonTextSize(context),
            ),
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ],
    );
  }

  Widget _buildLoading() => const SizedBox(
    height: 100,
    width: 100,
    child: Center(child: CircularProgressIndicator(color: Color(0xFF00FFFF))),
  );

  Widget _buildEmptyHistory(BuildContext context) => SizedBox(
    width: MediaQuery.of(context).size.width * 0.9,
    height: MediaQuery.of(context).size.height * 0.3,
    child: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.bubble_chart_outlined,
            size: 48,
            color: Colors.white70,
          ),
          const SizedBox(height: 16),
          Text(
            "You haven't been in any bubbles yet.",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white70,
              fontSize: _getBodyTextSize(context),
            ),
          ),
        ],
      ),
    ),
  );

  Widget _buildError(BuildContext context, String message) => SizedBox(
    height: 100,
    child: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 32),
          const SizedBox(height: 8),
          Text(
            message,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.red,
              fontSize: _getSubtitleTextSize(context),
            ),
          ),
        ],
      ),
    ),
  );

  Widget _buildBubbleHistory(BuildContext context, BubbleEntity bubble) =>
      SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.5,
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCurrentBubbleSection(context, bubble),
                const SizedBox(height: 24),
                _buildBubbleStatsSection(context, bubble),
                const SizedBox(height: 24),
                _buildMembersSection(context, bubble),
              ],
            ),
          ),
        ),
      );

  Widget _buildCurrentBubbleSection(
    BuildContext context,
    BubbleEntity bubble,
  ) => Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        'Current Bubble',
        style: TextStyle(
          color: Colors.white,
          fontSize: _getBodyTextSize(context),
          fontWeight: FontWeight.bold,
        ),
      ),
      const SizedBox(height: 12),
      Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              bubble.name.value,
              style: TextStyle(
                color: Colors.white,
                fontSize: _getBodyTextSize(context) + 2,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.access_time, size: 16, color: Colors.white70),
                const SizedBox(width: 4),
                Text(
                  'Created ${_formatDate(bubble.createdAt)}',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: _getSubtitleTextSize(context),
                  ),
                ),
              ],
            ),
            if (bubble.endDate != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.schedule, size: 16, color: Colors.white70),
                  const SizedBox(width: 4),
                  Text(
                    'Ends ${_formatDate(bubble.endDate!)} (${_calculateDaysRemaining(bubble)} days left)',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: _getSubtitleTextSize(context),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    ],
  );

  Widget _buildBubbleStatsSection(BuildContext context, BubbleEntity bubble) =>
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Bubble Stats',
            style: TextStyle(
              color: Colors.white,
              fontSize: _getBodyTextSize(context),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Members',
                  '${bubble.members.length}',
                  Icons.people,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Online',
                  '${bubble.onlineMembersCount}',
                  Icons.circle,
                  iconColor: Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Messages',
                  '${bubble.totalUnreadMessages}',
                  Icons.message,
                ),
              ),
            ],
          ),
        ],
      );

  Widget _buildStatCard(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    Color? iconColor,
  }) => Container(
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: Colors.white.withValues(alpha: 0.05),
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
    ),
    child: Column(
      children: [
        Icon(icon, size: 20, color: iconColor ?? Colors.white70),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: Colors.white,
            fontSize: _getBodyTextSize(context),
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white70,
            fontSize: _getSubtitleTextSize(context),
          ),
        ),
      ],
    ),
  );

  Widget _buildMembersSection(BuildContext context, BubbleEntity bubble) =>
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Members',
            style: TextStyle(
              color: Colors.white,
              fontSize: _getBodyTextSize(context),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...bubble.members.map((member) => _buildMemberTile(context, member)),
        ],
      );

  Widget _buildMemberTile(
    BuildContext context,
    BubbleMemberEntity member,
  ) => Container(
    margin: const EdgeInsets.only(bottom: 8),
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: Colors.white.withValues(alpha: 0.05),
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
    ),
    child: Row(
      children: [
        CircleAvatar(
          radius: 16,
          backgroundColor: Colors.white.withValues(alpha: 0.2),
          backgroundImage:
              member.avatarUrl != null ? NetworkImage(member.avatarUrl!) : null,
          child:
              member.avatarUrl == null
                  ? Text(
                    member.name.isNotEmpty ? member.name[0].toUpperCase() : '?',
                    style: const TextStyle(color: Colors.white),
                  )
                  : null,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                member.name,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: _getSubtitleTextSize(context) + 1,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                'Joined ${_formatDate(member.joinedAt)}',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: _getSubtitleTextSize(context) - 1,
                ),
              ),
            ],
          ),
        ),
        Row(
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: member.isOnline ? Colors.green : Colors.grey,
                shape: BoxShape.circle,
              ),
            ),
          ],
        ),
      ],
    ),
  );

  String _formatDate(DateTime date) => DateFormat('MMM d, y').format(date);

  int _calculateDaysRemaining(BubbleEntity bubble) {
    if (bubble.endDate == null) return 0;
    final now = DateTime.now();
    final remaining = bubble.endDate!.difference(now).inDays;
    return remaining > 0 ? remaining : 0;
  }
}
