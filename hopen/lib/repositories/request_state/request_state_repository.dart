import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';

/// Repository interface for managing request states across the application.
/// 
/// This repository provides a clean abstraction for request state management,
/// allowing the business logic layer to check and manage request states
/// without directly depending on the provider layer implementation.
/// 
/// Features:
/// - Check request status between users
/// - Mark requests as sent, completed, or declined
/// - Persistent state management across app restarts
/// - Cache management and synchronization
abstract class RequestStateRepository {
  /// Checks the status of a request between two users.
  /// 
  /// [currentUserId] - The current user's ID
  /// [targetUserId] - The target user's ID
  /// [requestType] - The type of request to check
  /// 
  /// Returns the current status of the request.
  Future<Result<RequestStatus>> checkRequestStatus({
    required String currentUserId,
    required String targetUserId,
    required RequestType requestType,
  });

  /// Marks a request as sent between two users.
  /// 
  /// [currentUserId] - The current user's ID
  /// [targetUserId] - The target user's ID
  /// [requestType] - The type of request being sent
  /// [requestId] - Optional request ID for tracking
  /// 
  /// Returns success or failure result.
  Future<Result<void>> markRequestSent({
    required String currentUserId,
    required String targetUserId,
    required RequestType requestType,
    String? requestId,
  });

  /// Marks a request as completed (accepted or fulfilled).
  /// 
  /// [currentUserId] - The current user's ID
  /// [targetUserId] - The target user's ID
  /// [requestType] - The type of request being completed
  /// 
  /// Returns success or failure result.
  Future<Result<void>> markRequestCompleted({
    required String currentUserId,
    required String targetUserId,
    required RequestType requestType,
  });

  /// Marks a request as declined.
  /// 
  /// [currentUserId] - The current user's ID
  /// [targetUserId] - The target user's ID
  /// [requestType] - The type of request being declined
  /// 
  /// Returns success or failure result.
  Future<Result<void>> markRequestDeclined({
    required String currentUserId,
    required String targetUserId,
    required RequestType requestType,
  });

  /// Initializes request state management for a user.
  /// 
  /// [userId] - The user ID to initialize for
  /// [forceRefresh] - Whether to force refresh from server
  /// 
  /// Returns the user model with request state information.
  Future<Result<UserModel?>> initializeForUser({
    required String userId,
    bool forceRefresh = false,
  });

  /// Refreshes request states from the server.
  /// 
  /// [userId] - The user ID to refresh for
  /// 
  /// Returns the updated user model.
  Future<Result<UserModel?>> refreshFromServer(String userId);

  /// Clears all cached request states.
  /// 
  /// Returns success or failure result.
  Future<Result<void>> clearCache();
}

/// Enum representing different types of requests in the system.
enum RequestType {
  contact,
  bubbleStart,
  bubbleInvite,
  bubbleJoin,
  bubblePropose,
  bubbleKickout,
}

/// Enum representing the status of a request.
enum RequestStatus {
  none,
  sentPending,
  receivedPending,
  accepted,
  declined,
  expired,
}

/// Extension to convert RequestType to string for API calls.
extension RequestTypeExtension on RequestType {
  String get value {
    switch (this) {
      case RequestType.contact:
        return 'contact';
      case RequestType.bubbleStart:
        return 'bubble_start';
      case RequestType.bubbleInvite:
        return 'bubble_invite';
      case RequestType.bubbleJoin:
        return 'bubble_join';
      case RequestType.bubblePropose:
        return 'bubble_propose';
      case RequestType.bubbleKickout:
        return 'bubble_kickout';
    }
  }
}

/// Extension to convert RequestStatus to string.
extension RequestStatusExtension on RequestStatus {
  String get value {
    switch (this) {
      case RequestStatus.none:
        return 'none';
      case RequestStatus.sentPending:
        return 'sent_pending';
      case RequestStatus.receivedPending:
        return 'received_pending';
      case RequestStatus.accepted:
        return 'accepted';
      case RequestStatus.declined:
        return 'declined';
      case RequestStatus.expired:
        return 'expired';
    }
  }
}
