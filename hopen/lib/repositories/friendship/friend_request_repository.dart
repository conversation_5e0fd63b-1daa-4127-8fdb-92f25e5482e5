import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/friend_request.dart';

/// Repository interface for managing auto-generated friend requests from bubble expiry
abstract class FriendRequestRepository {
  /// Get pending friend requests for the current user
  Future<Result<List<FriendRequest>>> getPendingFriendRequests();
  
  /// Accept a friend request by ID
  Future<Result<void>> acceptFriendRequest(String requestId);
  
  /// Decline a friend request by ID
  Future<Result<void>> declineFriendRequest(String requestId);
  
  /// Get a specific friend request by ID
  Future<Result<FriendRequest>> getFriendRequest(String requestId);
  
  /// Get list of current friends
  Future<Result<List<String>>> getFriends();
}
