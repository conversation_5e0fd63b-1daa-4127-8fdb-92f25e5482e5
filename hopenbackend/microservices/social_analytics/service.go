package social_analytics

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	authmw "hopenbackend/pkg/middleware"
	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
)

// Service handles social analytics operations using ArangoDB and PostgreSQL
type Service struct {
	logger     *zap.Logger
	arangoDB   *database.ArangoDBClient
	postgresql *database.PostgreSQLClient
	config     *config.Config
	oryClient  *ory.Client
}

// Dependencies holds the dependencies for the social analytics service
type Dependencies struct {
	Logger     *zap.Logger
	ArangoDB   *database.ArangoDBClient
	PostgreSQL *database.PostgreSQLClient
	Config     *config.Config
	OryClient  *ory.Client
}

// New creates a new social analytics service instance
func New(deps *Dependencies) *Service {
	return &Service{
		logger:     deps.Logger,
		arangoDB:   deps.ArangoDB,
		postgresql: deps.PostgreSQL,
		config:     deps.Config,
		oryClient:  deps.OryClient,
	}
}

// RegisterRoutes registers the social analytics service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	s.logger.Info("Registering social analytics routes")
	router.GET("/test", func(c *gin.Context) {
		s.logger.Info("Test route called")
		c.JSON(200, gin.H{"message": "test route works"})
	})
	router.GET("/enhanced-profile/:userId", s.getEnhancedProfile)
	router.GET("/profile-analytics", s.authMiddleware(), s.getProfileAnalytics)
	router.GET("/mutual-friends/:userId", s.authMiddleware(), s.getMutualFriends)
	router.GET("/mutual-contacts/:userId", s.authMiddleware(), s.getMutualContacts)
	router.GET("/common-bubbles/:userId", s.authMiddleware(), s.getCommonBubbles)
	router.GET("/connection-strength/:userId", s.authMiddleware(), s.getConnectionStrength)
	router.GET("/social-graph", s.authMiddleware(), s.getSocialGraph)
	router.GET("/engagement-metrics", s.authMiddleware(), s.getEngagementMetrics)
	s.logger.Info("Enhanced profile route registered", zap.String("path", "/enhanced-profile/:userId"))
}

// ProfileAnalytics represents comprehensive profile analytics
type ProfileAnalytics struct {
	UserID              string                 `json:"user_id"`
	MutualFriends       []string               `json:"mutual_friends"`
	MutualContacts      []string               `json:"mutual_contacts"`
	CommonBubbles       []CommonBubble         `json:"common_bubbles"`
	ConnectionStrength  float64                `json:"connection_strength"`
	SocialScore         float64                `json:"social_score"`
	EngagementMetrics   EngagementMetrics      `json:"engagement_metrics"`
	RecommendedActions  []string               `json:"recommended_actions"`
}

// EnhancedProfile represents a user profile with comprehensive relationship data
type EnhancedProfile struct {
	UserID                            string                    `json:"id"`
	Username                          string                    `json:"username"`
	Email                             string                    `json:"email"`
	FirstName                         string                    `json:"first_name"`
	LastName                          string                    `json:"last_name"`
	AvatarURL                         *string                   `json:"avatar_url"`
	BubbleID                          *string                   `json:"bubble_id"`
	IsOnline                          bool                      `json:"is_online"`
	FriendIds                         []string                  `json:"friend_ids"`
	ContactIds                        []string                  `json:"contact_ids"`
	BlockedUserIds                    []string                  `json:"blocked_user_ids"`
	PendingSentContactRequestIds      []string                  `json:"pending_sent_contact_request_ids"`
	PendingReceivedContactRequestIds  []string                  `json:"pending_received_contact_request_ids"`

	// Bubble Start Requests (no bubble exists yet)
	PendingSentBubbleStartRequestUserIds     []string `json:"pending_sent_bubble_start_request_user_ids"`
	PendingReceivedBubbleStartRequestUserIds []string `json:"pending_received_bubble_start_request_user_ids"`

	// Bubble Invite Requests (inviting someone to existing bubble)
	PendingSentBubbleInviteRequestUserIds    []string `json:"pending_sent_bubble_invite_request_user_ids"`
	PendingReceivedBubbleInviteRequestUserIds []string `json:"pending_received_bubble_invite_request_user_ids"`

	// Bubble Join Requests (requesting to join existing bubble)
	PendingSentBubbleJoinRequestBubbleIds    []string `json:"pending_sent_bubble_join_request_bubble_ids"`
	PendingReceivedBubbleJoinRequestUserIds  []string `json:"pending_received_bubble_join_request_user_ids"`

	// Bubble Propose Requests (proposing someone to join bubble)
	PendingSentBubbleProposeRequestUserIds   []string `json:"pending_sent_bubble_propose_request_user_ids"`
	PendingReceivedBubbleProposeRequestUserIds []string `json:"pending_received_bubble_propose_request_user_ids"`

	// Bubble Kickout Requests (requesting to remove someone from bubble)
	PendingSentBubbleKickoutRequestUserIds   []string `json:"pending_sent_bubble_kickout_request_user_ids"`
	PendingReceivedBubbleKickoutRequestUserIds []string `json:"pending_received_bubble_kickout_request_user_ids"`
}

// CommonBubble represents a bubble both users were/are members of
type CommonBubble struct {
	BubbleID    string `json:"bubble_id"`
	BubbleName  string `json:"bubble_name"`
	Status      string `json:"status"` // active, expired
	JoinedAt    string `json:"joined_at"`
	LeftAt      *string `json:"left_at,omitempty"`
}

// EngagementMetrics represents user engagement metrics
type EngagementMetrics struct {
	TotalBubbles        int     `json:"total_bubbles"`
	ActiveBubbles       int     `json:"active_bubbles"`
	TotalFriends        int     `json:"total_friends"`
	TotalContacts       int     `json:"total_contacts"`
	MessagesSent        int     `json:"messages_sent"`
	MessagesReceived    int     `json:"messages_received"`
	AvgResponseTime     float64 `json:"avg_response_time_minutes"`
	LastActiveAt        string  `json:"last_active_at"`
	EngagementScore     float64 `json:"engagement_score"`
}

// ConnectionStrength represents the strength of connection between two users
type ConnectionStrength struct {
	UserID              string  `json:"user_id"`
	TargetUserID        string  `json:"target_user_id"`
	Strength            float64 `json:"strength"` // 0.0 to 1.0
	MutualFriendsCount  int     `json:"mutual_friends_count"`
	MutualContactsCount int     `json:"mutual_contacts_count"`
	CommonBubblesCount  int     `json:"common_bubbles_count"`
	InteractionScore    float64 `json:"interaction_score"`
	Factors             []string `json:"factors"`
}

// SocialGraph represents a user's social network
type SocialGraph struct {
	UserID      string                 `json:"user_id"`
	Nodes       []SocialNode           `json:"nodes"`
	Edges       []SocialEdge           `json:"edges"`
	Clusters    []SocialCluster        `json:"clusters"`
	Metrics     SocialGraphMetrics     `json:"metrics"`
}

// SocialNode represents a person in the social graph
type SocialNode struct {
	UserID       string  `json:"user_id"`
	NodeType     string  `json:"node_type"` // friend, contact, bubble_member
	Centrality   float64 `json:"centrality"`
	ClusterID    string  `json:"cluster_id"`
}

// SocialEdge represents a relationship in the social graph
type SocialEdge struct {
	FromUserID   string  `json:"from_user_id"`
	ToUserID     string  `json:"to_user_id"`
	EdgeType     string  `json:"edge_type"` // friendship, contact, bubble_membership
	Weight       float64 `json:"weight"`
	CreatedAt    string  `json:"created_at"`
}

// SocialCluster represents a cluster of closely connected users
type SocialCluster struct {
	ClusterID   string   `json:"cluster_id"`
	Members     []string `json:"members"`
	Cohesion    float64  `json:"cohesion"`
	Description string   `json:"description"`
}

// SocialGraphMetrics represents metrics about the social graph
type SocialGraphMetrics struct {
	TotalNodes          int     `json:"total_nodes"`
	TotalEdges          int     `json:"total_edges"`
	Density             float64 `json:"density"`
	ClusteringCoeff     float64 `json:"clustering_coefficient"`
	AvgPathLength       float64 `json:"avg_path_length"`
	CentralityScore     float64 `json:"centrality_score"`
}

// getProfileAnalytics handles getting comprehensive profile analytics
func (s *Service) getProfileAnalytics(c *gin.Context) {
	userID, _ := c.Get("user_id")
	targetUserID := c.Query("target_user_id")

	if targetUserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Target user ID is required"})
		return
	}

	// Get mutual friends
	mutualFriends, err := s.arangoDB.GetMutualFriends(c.Request.Context(), userID.(string), targetUserID)
	if err != nil {
		s.logger.Error("Failed to get mutual friends", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get analytics"})
		return
	}

	// Get mutual contacts
	mutualContacts, err := s.getMutualContactsData(c.Request.Context(), userID.(string), targetUserID)
	if err != nil {
		s.logger.Error("Failed to get mutual contacts", zap.Error(err))
		mutualContacts = []string{} // Continue with empty list
	}

	// Get common bubbles
	commonBubbles, err := s.getCommonBubblesData(c.Request.Context(), userID.(string), targetUserID)
	if err != nil {
		s.logger.Error("Failed to get common bubbles", zap.Error(err))
		commonBubbles = []CommonBubble{} // Continue with empty list
	}

	// Calculate connection strength
	connectionStrength := s.calculateConnectionStrength(len(mutualFriends), len(mutualContacts), len(commonBubbles))

	// Get engagement metrics
	engagementMetrics, err := s.getEngagementMetricsData(c.Request.Context(), targetUserID)
	if err != nil {
		s.logger.Error("Failed to get engagement metrics", zap.Error(err))
		engagementMetrics = EngagementMetrics{} // Continue with empty metrics
	}

	// Calculate social score
	socialScore := s.calculateSocialScore(engagementMetrics, connectionStrength)

	// Generate recommended actions
	recommendedActions := s.generateRecommendedActions(len(mutualFriends), len(mutualContacts), len(commonBubbles))

	analytics := &ProfileAnalytics{
		UserID:             targetUserID,
		MutualFriends:      mutualFriends,
		MutualContacts:     mutualContacts,
		CommonBubbles:      commonBubbles,
		ConnectionStrength: connectionStrength,
		SocialScore:        socialScore,
		EngagementMetrics:  engagementMetrics,
		RecommendedActions: recommendedActions,
	}

	c.JSON(http.StatusOK, analytics)
}

// getMutualFriends handles getting mutual friends between two users
func (s *Service) getMutualFriends(c *gin.Context) {
	userID, _ := c.Get("user_id")
	targetUserID := c.Param("userId")

	if targetUserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	mutualFriends, err := s.arangoDB.GetMutualFriends(c.Request.Context(), userID.(string), targetUserID)
	if err != nil {
		s.logger.Error("Failed to get mutual friends", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get mutual friends"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"mutual_friends": mutualFriends,
		"count":          len(mutualFriends),
	})
}

// getMutualContacts handles getting mutual contacts between two users
func (s *Service) getMutualContacts(c *gin.Context) {
	userID, _ := c.Get("user_id")
	targetUserID := c.Param("userId")

	if targetUserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	mutualContacts, err := s.getMutualContactsData(c.Request.Context(), userID.(string), targetUserID)
	if err != nil {
		s.logger.Error("Failed to get mutual contacts", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get mutual contacts"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"mutual_contacts": mutualContacts,
		"count":           len(mutualContacts),
	})
}

// getCommonBubbles handles getting common bubbles between two users
func (s *Service) getCommonBubbles(c *gin.Context) {
	userID, _ := c.Get("user_id")
	targetUserID := c.Param("userId")

	if targetUserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	commonBubbles, err := s.getCommonBubblesData(c.Request.Context(), userID.(string), targetUserID)
	if err != nil {
		s.logger.Error("Failed to get common bubbles", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get common bubbles"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"common_bubbles": commonBubbles,
		"count":          len(commonBubbles),
	})
}

// getConnectionStrength handles getting connection strength between two users
func (s *Service) getConnectionStrength(c *gin.Context) {
	userID, _ := c.Get("user_id")
	targetUserID := c.Param("userId")

	if targetUserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	// Get all connection data
	mutualFriends, _ := s.arangoDB.GetMutualFriends(c.Request.Context(), userID.(string), targetUserID)
	mutualContacts, _ := s.getMutualContactsData(c.Request.Context(), userID.(string), targetUserID)
	commonBubbles, _ := s.getCommonBubblesData(c.Request.Context(), userID.(string), targetUserID)

	// Calculate interaction score (simplified)
	interactionScore := 0.5 // Would be calculated from message history, etc.

	strength := s.calculateConnectionStrength(len(mutualFriends), len(mutualContacts), len(commonBubbles))

	factors := []string{}
	if len(mutualFriends) > 0 {
		factors = append(factors, "mutual_friends")
	}
	if len(mutualContacts) > 0 {
		factors = append(factors, "mutual_contacts")
	}
	if len(commonBubbles) > 0 {
		factors = append(factors, "common_bubbles")
	}

	connectionStrength := &ConnectionStrength{
		UserID:              userID.(string),
		TargetUserID:        targetUserID,
		Strength:            strength,
		MutualFriendsCount:  len(mutualFriends),
		MutualContactsCount: len(mutualContacts),
		CommonBubblesCount:  len(commonBubbles),
		InteractionScore:    interactionScore,
		Factors:             factors,
	}

	c.JSON(http.StatusOK, connectionStrength)
}

// getSocialGraph handles getting user's social graph
func (s *Service) getSocialGraph(c *gin.Context) {
	userID, _ := c.Get("user_id")
	depth, _ := strconv.Atoi(c.DefaultQuery("depth", "2"))

	if depth > 3 {
		depth = 3 // Limit depth to prevent performance issues
	}

	// Build social graph (simplified implementation)
	socialGraph := &SocialGraph{
		UserID: userID.(string),
		Nodes: []SocialNode{
			{
				UserID:     userID.(string),
				NodeType:   "self",
				Centrality: 1.0,
				ClusterID:  "cluster_1",
			},
		},
		Edges: []SocialEdge{},
		Clusters: []SocialCluster{
			{
				ClusterID:   "cluster_1",
				Members:     []string{userID.(string)},
				Cohesion:    0.8,
				Description: "Primary social circle",
			},
		},
		Metrics: SocialGraphMetrics{
			TotalNodes:      1,
			TotalEdges:      0,
			Density:         0.0,
			ClusteringCoeff: 0.0,
			AvgPathLength:   0.0,
			CentralityScore: 1.0,
		},
	}

	c.JSON(http.StatusOK, socialGraph)
}

// getEngagementMetrics handles getting user engagement metrics
func (s *Service) getEngagementMetrics(c *gin.Context) {
	userID, _ := c.Get("user_id")

	metrics, err := s.getEngagementMetricsData(c.Request.Context(), userID.(string))
	if err != nil {
		s.logger.Error("Failed to get engagement metrics", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get engagement metrics"})
		return
	}

	c.JSON(http.StatusOK, metrics)
}

// Helper methods

// getMutualContactsData retrieves mutual contacts between two users
func (s *Service) getMutualContactsData(ctx context.Context, userID1, userID2 string) ([]string, error) {
	query := `
		LET user1_contacts = (
			FOR contact IN contacts
			FILTER (contact.requester_id == @userID1 OR contact.recipient_id == @userID1)
			AND contact.status == "accepted"
			RETURN contact.requester_id == @userID1 ? contact.recipient_id : contact.requester_id
		)

		LET user2_contacts = (
			FOR contact IN contacts
			FILTER (contact.requester_id == @userID2 OR contact.recipient_id == @userID2)
			AND contact.status == "accepted"
			RETURN contact.requester_id == @userID2 ? contact.recipient_id : contact.requester_id
		)

		LET mutual_contacts = INTERSECTION(user1_contacts, user2_contacts)

		FOR contact_id IN mutual_contacts
		RETURN contact_id`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"userID1": userID1,
		"userID2": userID2,
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close()

	var mutualContacts []string
	for cursor.HasMore() {
		var contactID string
		_, err := cursor.ReadDocument(ctx, &contactID)
		if err != nil {
			continue
		}
		mutualContacts = append(mutualContacts, contactID)
	}

	return mutualContacts, nil
}

// getCommonBubblesData retrieves common bubbles between two users
func (s *Service) getCommonBubblesData(ctx context.Context, userID1, userID2 string) ([]CommonBubble, error) {
	query := `
		LET user1_bubbles = (
			FOR membership IN bubble_memberships
			FILTER membership.user_id == @userID1
			RETURN membership.bubble_id
		)

		LET user2_bubbles = (
			FOR membership IN bubble_memberships
			FILTER membership.user_id == @userID2
			RETURN membership.bubble_id
		)

		LET common_bubble_ids = INTERSECTION(user1_bubbles, user2_bubbles)

		FOR bubble_id IN common_bubble_ids
		LET membership1 = (
			FOR m IN bubble_memberships
			FILTER m.bubble_id == bubble_id AND m.user_id == @userID1
			RETURN m
		)[0]
		RETURN {
			bubble_id: bubble_id,
			bubble_name: CONCAT("Bubble ", bubble_id),
			status: membership1.status,
			joined_at: membership1.joined_at,
			left_at: membership1.status == "left" ? membership1.left_at : null
		}`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"userID1": userID1,
		"userID2": userID2,
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close()

	var commonBubbles []CommonBubble
	for cursor.HasMore() {
		var bubble CommonBubble
		_, err := cursor.ReadDocument(ctx, &bubble)
		if err != nil {
			continue
		}
		commonBubbles = append(commonBubbles, bubble)
	}

	return commonBubbles, nil
}

// getEngagementMetricsData retrieves engagement metrics for a user
func (s *Service) getEngagementMetricsData(ctx context.Context, userID string) (EngagementMetrics, error) {
	// Get bubble count from ArangoDB
	bubbleQuery := `
		LET total_bubbles = LENGTH(
			FOR membership IN bubble_memberships
			FILTER membership.user_id == @userID
			RETURN membership
		)

		LET active_bubbles = LENGTH(
			FOR membership IN bubble_memberships
			FILTER membership.user_id == @userID AND membership.status == "active"
			RETURN membership
		)

		RETURN {
			total_bubbles: total_bubbles,
			active_bubbles: active_bubbles
		}`

	cursor, err := s.arangoDB.Database.Query(ctx, bubbleQuery, map[string]interface{}{
		"userID": userID,
	})
	if err != nil {
		return EngagementMetrics{}, err
	}
	defer cursor.Close()

	var bubbleMetrics struct {
		TotalBubbles  int `json:"total_bubbles"`
		ActiveBubbles int `json:"active_bubbles"`
	}

	if cursor.HasMore() {
		cursor.ReadDocument(ctx, &bubbleMetrics)
	}

	// Get friend count from ArangoDB
	friendQuery := `
		RETURN LENGTH(
			FOR friendship IN friendships
			FILTER friendship.user1_id == @userID OR friendship.user2_id == @userID
			RETURN friendship
		)`

	friendCursor, err := s.arangoDB.Database.Query(ctx, friendQuery, map[string]interface{}{
		"userID": userID,
	})
	if err != nil {
		return EngagementMetrics{}, err
	}
	defer friendCursor.Close()

	var totalFriends int
	if friendCursor.HasMore() {
		friendCursor.ReadDocument(ctx, &totalFriends)
	}

	// Get contact count from ArangoDB
	contactQuery := `
		RETURN LENGTH(
			FOR contact IN contacts
			FILTER (contact.requester_id == @userID OR contact.recipient_id == @userID)
			AND contact.status == "accepted"
			RETURN contact
		)`

	contactCursor, err := s.arangoDB.Database.Query(ctx, contactQuery, map[string]interface{}{
		"userID": userID,
	})
	if err != nil {
		return EngagementMetrics{}, err
	}
	defer contactCursor.Close()

	var totalContacts int
	if contactCursor.HasMore() {
		contactCursor.ReadDocument(ctx, &totalContacts)
	}

	// Calculate engagement score
	engagementScore := s.calculateEngagementScore(bubbleMetrics.TotalBubbles, totalFriends, totalContacts)

	return EngagementMetrics{
		TotalBubbles:     bubbleMetrics.TotalBubbles,
		ActiveBubbles:    bubbleMetrics.ActiveBubbles,
		TotalFriends:     totalFriends,
		TotalContacts:    totalContacts,
		MessagesSent:     0, // Would be calculated from Cassandra
		MessagesReceived: 0, // Would be calculated from Cassandra
		AvgResponseTime:  0, // Would be calculated from message timestamps
		LastActiveAt:     "2024-01-01T00:00:00Z", // Would be from user activity tracking
		EngagementScore:  engagementScore,
	}, nil
}

// calculateConnectionStrength calculates connection strength between users
func (s *Service) calculateConnectionStrength(mutualFriends, mutualContacts, commonBubbles int) float64 {
	// Weighted scoring system
	friendWeight := 0.4
	contactWeight := 0.3
	bubbleWeight := 0.3

	// Normalize scores (assuming max values)
	maxFriends := 50.0
	maxContacts := 100.0
	maxBubbles := 10.0

	friendScore := float64(mutualFriends) / maxFriends
	if friendScore > 1.0 {
		friendScore = 1.0
	}

	contactScore := float64(mutualContacts) / maxContacts
	if contactScore > 1.0 {
		contactScore = 1.0
	}

	bubbleScore := float64(commonBubbles) / maxBubbles
	if bubbleScore > 1.0 {
		bubbleScore = 1.0
	}

	return (friendScore * friendWeight) + (contactScore * contactWeight) + (bubbleScore * bubbleWeight)
}

// calculateSocialScore calculates overall social score
func (s *Service) calculateSocialScore(metrics EngagementMetrics, connectionStrength float64) float64 {
	// Weighted scoring system
	engagementWeight := 0.6
	connectionWeight := 0.4

	// Normalize engagement score (0-100 scale)
	normalizedEngagement := metrics.EngagementScore / 100.0
	if normalizedEngagement > 1.0 {
		normalizedEngagement = 1.0
	}

	return (normalizedEngagement * engagementWeight) + (connectionStrength * connectionWeight)
}

// calculateEngagementScore calculates user engagement score
func (s *Service) calculateEngagementScore(totalBubbles, totalFriends, totalContacts int) float64 {
	// Simple scoring algorithm
	bubbleScore := float64(totalBubbles) * 10.0
	friendScore := float64(totalFriends) * 5.0
	contactScore := float64(totalContacts) * 2.0

	totalScore := bubbleScore + friendScore + contactScore

	// Cap at 100
	if totalScore > 100.0 {
		totalScore = 100.0
	}

	return totalScore
}

// generateRecommendedActions generates recommended actions based on analytics
func (s *Service) generateRecommendedActions(mutualFriends, mutualContacts, commonBubbles int) []string {
	actions := []string{}

	if mutualFriends > 0 {
		actions = append(actions, "Consider strengthening friendship through shared activities")
	}

	if mutualContacts > 3 {
		actions = append(actions, "You have many mutual contacts - consider creating a bubble together")
	}

	if commonBubbles > 0 {
		actions = append(actions, "You've shared bubble experiences - great foundation for friendship")
	}

	if len(actions) == 0 {
		actions = append(actions, "Send a contact request to start building your connection")
	}

	return actions
}

// getEnhancedProfile handles getting enhanced user profile with relationship data
func (s *Service) getEnhancedProfile(c *gin.Context) {
	s.logger.Info("getEnhancedProfile called", zap.String("path", c.Request.URL.Path))
	targetUserID := c.Param("userId")
	s.logger.Info("getEnhancedProfile", zap.String("targetUserID", targetUserID))

	if targetUserID == "" {
		s.logger.Error("getEnhancedProfile: User ID is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	// Get basic user data from user service (internal call)
	basicUser, err := s.getUserFromUserService(c.Request.Context(), targetUserID)
	if err != nil {
		s.logger.Error("Failed to get user from user service", zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Get relationship data
	contactIds, err := s.getContactIds(c.Request.Context(), targetUserID)
	if err != nil {
		s.logger.Error("Failed to get contact IDs", zap.Error(err))
		contactIds = []string{} // Continue with empty list
	}

	friendIds, err := s.getFriendIds(c.Request.Context(), targetUserID)
	if err != nil {
		s.logger.Error("Failed to get friend IDs", zap.Error(err))
		friendIds = []string{} // Continue with empty list
	}

	// Get pending contact requests
	pendingSentContactIds, pendingReceivedContactIds, err := s.getPendingContactRequests(c.Request.Context(), targetUserID)
	if err != nil {
		s.logger.Error("Failed to get pending contact requests", zap.Error(err))
		pendingSentContactIds = []string{}
		pendingReceivedContactIds = []string{}
	}

	// Get all types of pending bubble requests
	bubbleRequests, err := s.getAllPendingBubbleRequests(c.Request.Context(), targetUserID)
	if err != nil {
		s.logger.Error("Failed to get pending bubble requests", zap.Error(err))
		bubbleRequests = &BubbleRequestSummary{}
	}

	// Get bubble ID from bubble service
	bubbleID, err := s.getUserBubbleId(c.Request.Context(), targetUserID)
	if err != nil {
		s.logger.Error("Failed to get user bubble ID", zap.Error(err))
		bubbleID = nil // Continue without bubble ID
	}

	// Build enhanced profile
	enhancedProfile := &EnhancedProfile{
		UserID:                            basicUser.ID,
		Username:                          basicUser.Username,
		Email:                             basicUser.Email,
		FirstName:                         basicUser.FirstName,
		LastName:                          basicUser.LastName,
		AvatarURL:                         basicUser.AvatarURL,
		BubbleID:                          bubbleID,
		IsOnline:                          false, // Would be from presence service
		FriendIds:                         friendIds,
		ContactIds:                        contactIds,
		BlockedUserIds:                    []string{}, // Would be from user service
		PendingSentContactRequestIds:      pendingSentContactIds,
		PendingReceivedContactRequestIds:  pendingReceivedContactIds,

		// Legacy bubble request fields (for backward compatibility)
		// For now, we populate these with start requests since that's the most common use case
		PendingSentBubbleRequestUserIds:     bubbleRequests.SentStartRequests,
		PendingReceivedBubbleRequestUserIds: bubbleRequests.ReceivedStartRequests,

		// Bubble Start Requests
		PendingSentBubbleStartRequestUserIds:     bubbleRequests.SentStartRequests,
		PendingReceivedBubbleStartRequestUserIds: bubbleRequests.ReceivedStartRequests,

		// Bubble Invite Requests
		PendingSentBubbleInviteRequestUserIds:    bubbleRequests.SentInviteRequests,
		PendingReceivedBubbleInviteRequestUserIds: bubbleRequests.ReceivedInviteRequests,

		// Bubble Join Requests
		PendingSentBubbleJoinRequestBubbleIds:    bubbleRequests.SentJoinRequests,
		PendingReceivedBubbleJoinRequestUserIds:  bubbleRequests.ReceivedJoinRequests,

		// Bubble Propose Requests
		PendingSentBubbleProposeRequestUserIds:   bubbleRequests.SentProposeRequests,
		PendingReceivedBubbleProposeRequestUserIds: bubbleRequests.ReceivedProposeRequests,

		// Bubble Kickout Requests
		PendingSentBubbleKickoutRequestUserIds:   bubbleRequests.SentKickoutRequests,
		PendingReceivedBubbleKickoutRequestUserIds: bubbleRequests.ReceivedKickoutRequests,
	}

	c.JSON(http.StatusOK, enhancedProfile)
}

// Helper methods for internal service calls

// BasicUser represents basic user data from user service
type BasicUser struct {
	ID        string  `json:"id"`
	Username  string  `json:"username"`
	Email     string  `json:"email"`
	FirstName string  `json:"first_name"`
	LastName  string  `json:"last_name"`
	AvatarURL *string `json:"avatar_url"`
}

// BubbleRequestSummary represents all pending bubble requests for a user
type BubbleRequestSummary struct {
	SentStartRequests     []string `json:"sent_start_requests"`
	ReceivedStartRequests []string `json:"received_start_requests"`
	SentInviteRequests    []string `json:"sent_invite_requests"`
	ReceivedInviteRequests []string `json:"received_invite_requests"`
	SentJoinRequests      []string `json:"sent_join_requests"`
	ReceivedJoinRequests  []string `json:"received_join_requests"`
	SentProposeRequests   []string `json:"sent_propose_requests"`
	ReceivedProposeRequests []string `json:"received_propose_requests"`
	SentKickoutRequests   []string `json:"sent_kickout_requests"`
	ReceivedKickoutRequests []string `json:"received_kickout_requests"`
}

// getUserFromUserService gets basic user data from user service
func (s *Service) getUserFromUserService(ctx context.Context, userID string) (*BasicUser, error) {
	url := fmt.Sprintf("http://user-service:8080/api/v1/users/%s", userID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("user service returned status %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var user BasicUser
	if err := json.Unmarshal(body, &user); err != nil {
		return nil, err
	}

	return &user, nil
}

// getContactIds gets contact IDs for a user from ArangoDB
func (s *Service) getContactIds(ctx context.Context, userID string) ([]string, error) {
	query := `
		FOR contact IN contacts
		FILTER (contact.requester_id == @userID OR contact.recipient_id == @userID)
		AND contact.status == "accepted"
		RETURN contact.requester_id == @userID ? contact.recipient_id : contact.requester_id`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"userID": userID,
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close()

	var contactIds []string
	for cursor.HasMore() {
		var contactID string
		_, err := cursor.ReadDocument(ctx, &contactID)
		if err != nil {
			continue
		}
		contactIds = append(contactIds, contactID)
	}

	return contactIds, nil
}

// getFriendIds gets friend IDs for a user from ArangoDB
func (s *Service) getFriendIds(ctx context.Context, userID string) ([]string, error) {
	query := `
		FOR friendship IN friendships
		FILTER friendship.user1_id == @userID OR friendship.user2_id == @userID
		RETURN friendship.user1_id == @userID ? friendship.user2_id : friendship.user1_id`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"userID": userID,
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close()

	var friendIds []string
	for cursor.HasMore() {
		var friendID string
		_, err := cursor.ReadDocument(ctx, &friendID)
		if err != nil {
			continue
		}
		friendIds = append(friendIds, friendID)
	}

	return friendIds, nil
}

// getPendingContactRequests gets pending contact request IDs for a user
func (s *Service) getPendingContactRequests(ctx context.Context, userID string) ([]string, []string, error) {
	// Get sent requests
	sentQuery := `
		FOR contact IN contacts
		FILTER contact.requester_id == @userID AND contact.status == "pending"
		RETURN contact.recipient_id`

	sentCursor, err := s.arangoDB.Database.Query(ctx, sentQuery, map[string]interface{}{
		"userID": userID,
	})
	if err != nil {
		return nil, nil, err
	}
	defer sentCursor.Close()

	var sentIds []string
	for sentCursor.HasMore() {
		var recipientID string
		_, err := sentCursor.ReadDocument(ctx, &recipientID)
		if err != nil {
			continue
		}
		sentIds = append(sentIds, recipientID)
	}

	// Get received requests
	receivedQuery := `
		FOR contact IN contacts
		FILTER contact.recipient_id == @userID AND contact.status == "pending"
		RETURN contact.requester_id`

	receivedCursor, err := s.arangoDB.Database.Query(ctx, receivedQuery, map[string]interface{}{
		"userID": userID,
	})
	if err != nil {
		return sentIds, nil, err
	}
	defer receivedCursor.Close()

	var receivedIds []string
	for receivedCursor.HasMore() {
		var requesterID string
		_, err := receivedCursor.ReadDocument(ctx, &requesterID)
		if err != nil {
			continue
		}
		receivedIds = append(receivedIds, requesterID)
	}

	return sentIds, receivedIds, nil
}

// getAllPendingBubbleRequests gets all types of pending bubble requests for a user
func (s *Service) getAllPendingBubbleRequests(ctx context.Context, userID string) (*BubbleRequestSummary, error) {
	summary := &BubbleRequestSummary{
		SentStartRequests:     []string{},
		ReceivedStartRequests: []string{},
		SentInviteRequests:    []string{},
		ReceivedInviteRequests: []string{},
		SentJoinRequests:      []string{},
		ReceivedJoinRequests:  []string{},
		SentProposeRequests:   []string{},
		ReceivedProposeRequests: []string{},
		SentKickoutRequests:   []string{},
		ReceivedKickoutRequests: []string{},
	}

	// Get sent requests by type
	sentQuery := `
		SELECT
			COALESCE(target_user_id, recipient_id) as target_id,
			request_type
		FROM bubble_requests
		WHERE requester_id = $1 AND status = 'pending'`

	sentRows, err := s.postgresql.Pool.Query(ctx, sentQuery, userID)
	if err != nil {
		return summary, err
	}
	defer sentRows.Close()

	for sentRows.Next() {
		var targetID, requestType string
		if err := sentRows.Scan(&targetID, &requestType); err != nil {
			continue
		}

		switch requestType {
		case "start":
			summary.SentStartRequests = append(summary.SentStartRequests, targetID)
		case "invite":
			summary.SentInviteRequests = append(summary.SentInviteRequests, targetID)
		case "join":
			summary.SentJoinRequests = append(summary.SentJoinRequests, targetID)
		case "propose":
			summary.SentProposeRequests = append(summary.SentProposeRequests, targetID)
		case "kick":
			summary.SentKickoutRequests = append(summary.SentKickoutRequests, targetID)
		}
	}

	// Get received requests by type
	receivedQuery := `
		SELECT
			requester_id,
			request_type
		FROM bubble_requests
		WHERE (target_user_id = $1 OR recipient_id = $1) AND status = 'pending'`

	receivedRows, err := s.postgresql.Pool.Query(ctx, receivedQuery, userID)
	if err != nil {
		return summary, err
	}
	defer receivedRows.Close()

	for receivedRows.Next() {
		var requesterID, requestType string
		if err := receivedRows.Scan(&requesterID, &requestType); err != nil {
			continue
		}

		switch requestType {
		case "start":
			summary.ReceivedStartRequests = append(summary.ReceivedStartRequests, requesterID)
		case "invite":
			summary.ReceivedInviteRequests = append(summary.ReceivedInviteRequests, requesterID)
		case "join":
			summary.ReceivedJoinRequests = append(summary.ReceivedJoinRequests, requesterID)
		case "propose":
			summary.ReceivedProposeRequests = append(summary.ReceivedProposeRequests, requesterID)
		case "kick":
			summary.ReceivedKickoutRequests = append(summary.ReceivedKickoutRequests, requesterID)
		}
	}

	return summary, nil
}

// getUserBubbleId gets the current bubble ID for a user from PostgreSQL
func (s *Service) getUserBubbleId(ctx context.Context, userID string) (*string, error) {
	query := `
		SELECT bubble_id FROM bubbles
		WHERE (user1_id = $1 OR user2_id = $1)
		AND status = 'active'
		ORDER BY created_at DESC
		LIMIT 1`

	var bubbleID string
	err := s.postgresql.Pool.QueryRow(ctx, query, userID).Scan(&bubbleID)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return nil, nil // No active bubble
		}
		return nil, err
	}

	return &bubbleID, nil
}

// authMiddleware provides authentication middleware
func (s *Service) authMiddleware() gin.HandlerFunc {
	return authmw.NewAuthMiddleware(s.oryClient, s.logger)
}
